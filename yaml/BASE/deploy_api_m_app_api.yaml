apiVersion: apps/v1
kind: Deployment
metadata:
  name: apimanagement-api
  labels:
    app: apimanagement-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: apimanagement-api
  template:
    metadata:
      labels:
        app: apimanagement-api
    spec:
      containers:
        - name: api-management-go
          image: {image}
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh", "-c"]
          args: ["cd $API_M_HOME; ./web"]
          envFrom:
            - configMapRef:
                name: mobio-config
            - secretRef:
                name: mobio-secret
          ports:
            - containerPort: 80
          resources:
            requests:
              memory: 70Mi
              cpu: 80m
            limits:
              memory: 1Gi
              cpu: 500m
          volumeMounts:
            - name: mobio-shared-data
              mountPath: /media/data/resources/
          startupProbe:
            tcpSocket:
              port: 80
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              port: 80
              path: /api/v1.0/ping
            initialDelaySeconds: 60
            periodSeconds: 30
          livenessProbe:
            httpGet:
              port: 80
              path: /api/v1.0/ping
            initialDelaySeconds: 120
            periodSeconds: 5
            timeoutSeconds: 4
            failureThreshold: 3
      imagePullSecrets:
        - name: registrypullsecret
      volumes:
        - name: mobio-shared-data
          persistentVolumeClaim:
            claimName: mobio-resources-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: api-management-app-api-service
  labels:
    app: api-management-app-api
spec:
  ports:
    - port: 80
      protocol: TCP
  selector:
    app: apimanagement-api
