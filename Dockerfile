FROM golang:1.22-alpine AS builder

WORKDIR /home/<USER>/projects/build/

COPY go.mod go.sum /home/<USER>/projects/build/
RUN go mod download
COPY . /home/<USER>/projects/build/

ENV CGO_ENABLED=0 GOOS=linux GOARCH=amd64
RUN go build -ldflags="-s -w" -o web cmd/web.go
RUN go build -ldflags="-s -w" -o guest cmd/guest.go
RUN go build -ldflags="-s -w" -o public_pu cmd/public_pu.go
RUN go build -ldflags="-s -w" -o partner cmd/partner.go

FROM golang:1.22-alpine AS runner

RUN echo "@edge https://dl-cdn.alpinelinux.org/alpine/edge/main" >> /etc/apk/repositories && \
    apk update && \
    apk add --no-cache musl-utils@edge musl@edge

# Cài các gói khác từ edge (nếu cần)
RUN apk add --no-cache \
    git@edge \
    curl@edge \
    curl-dev@edge \
    libcrypto3=3.5.1-r0 \
    libssl3=3.5.1-r0 \
    openssl-dev=3.5.1-r0 

# Cập nhật các gói hệ thống
RUN apk upgrade --no-cache

WORKDIR /home/<USER>/projects/api-m/
COPY /config/lang/language /home/<USER>/projects/api-m/config/lang/language

ENV LC_ALL=en_US.UTF-8\
    API_M_HOME="/home/<USER>/projects/api-m/"\
    TIME_SECOND_CACHE_PERMISSION=180\
    SEND_PEM=true

COPY --from=builder /home/<USER>/projects/build/web /home/<USER>/projects/api-m/
COPY --from=builder /home/<USER>/projects/build/guest /home/<USER>/projects/api-m/
COPY --from=builder /home/<USER>/projects/build/public_pu /home/<USER>/projects/api-m/
COPY --from=builder /home/<USER>/projects/build/partner /home/<USER>/projects/api-m/
#COPY --from=builder /home/<USER>/projects/build/olap /home/<USER>/projects/api-m/

CMD tail -f /dev/null

