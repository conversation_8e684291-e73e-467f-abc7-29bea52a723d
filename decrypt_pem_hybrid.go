package main

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"net/url"
	"os"
	"os/exec"
	"strings"
)

// decryptPemHybrid - Go function that calls Node.js for the actual decryption
func decryptPemHybrid(encryptedData, u string) (string, error) {
	// Step 1: URL decode the encrypted data
	decodedData, err := url.QueryUnescape(encryptedData)
	if err != nil {
		return "", fmt.Errorf("failed to URL decode: %v", err)
	}
	fmt.Printf("Decoded data: %s\n", decodedData)

	// Step 2: JWT token (hardcoded as in the original JS)
	j := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5S87kehv0W3NMQRh4CIG5s3yjsQ0tUpXw5NLLSc7zs0"

	// Step 3: Extract the last part after the final dot
	parts := strings.Split(j, ".")
	var lastPart string
	if len(parts) > 0 {
		lastPart = parts[len(parts)-1]
	}
	fmt.Printf("Last part of JWT: %s\n", lastPart)

	// Step 4: Generate MD5 hash and take first 24 characters
	md5Hash := md5.Sum([]byte(lastPart + u))
	k := hex.EncodeToString(md5Hash[:])[:24]
	fmt.Printf("Key k (24 chars): %s\n", k)

	// Step 5: Call Node.js to perform the actual decryption
	result, err := callNodeJSDecrypt(decodedData, k)
	if err != nil {
		return "", fmt.Errorf("Node.js decryption failed: %v", err)
	}

	return result, nil
}

// callNodeJSDecrypt calls Node.js script to perform CryptoJS decryption
func callNodeJSDecrypt(encryptedData, key string) (string, error) {
	// Create a temporary Node.js script
	jsScript := fmt.Sprintf(`
const CryptoES = require('crypto-es').default;

const encryptedData = %q;
const k = %q;

try {
    const keyObj = CryptoES.enc.Hex.parse(k);
    const ivObj = CryptoES.enc.Hex.parse(k);
    const mode = CryptoES.mode.CBC;
    const padding = CryptoES.pad.Pkcs7;
    const options = { iv: ivObj, mode: mode, padding: padding };
    
    const decrypted = CryptoES.AES.decrypt(encryptedData, keyObj, options);
    const decryptedText = decrypted.toString(CryptoES.enc.Utf8);
    
    console.log(decryptedText);
} catch (error) {
    console.error('Decryption error:', error.message);
    process.exit(1);
}
`, encryptedData, key)

	// Write script to temporary file
	tmpFile := "temp_decrypt.js"
	err := os.WriteFile(tmpFile, []byte(jsScript), 0644)
	if err != nil {
		return "", fmt.Errorf("failed to write temp script: %v", err)
	}
	defer os.Remove(tmpFile)

	// Execute Node.js script
	cmd := exec.Command("node", tmpFile)
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("failed to execute Node.js: %v", err)
	}

	result := strings.TrimSpace(string(output))
	return result, nil
}

// Pure Go implementation (for reference, doesn't work correctly)
func decryptPemPureGo(encryptedData, u string) (string, error) {
	// This is the pure Go implementation that doesn't work correctly
	// but shows the correct logic structure

	_, _ = url.QueryUnescape(encryptedData)
	j := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5S87kehv0W3NMQRh4CIG5s3yjsQ0tUpXw5NLLSc7zs0"

	parts := strings.Split(j, ".")
	lastPart := parts[len(parts)-1]

	md5Hash := md5.Sum([]byte(lastPart + u))
	_ = hex.EncodeToString(md5Hash[:])[:24]

	// This is where the pure Go implementation fails
	// because CryptoJS handles 12-byte keys differently
	return "Pure Go implementation - doesn't work correctly", nil
}

func main() {
	fmt.Println("=== Hybrid Go + Node.js AES-CBC Decryption ===")
	fmt.Println()

	// Test case
	encryptedData := "cRU%2BNwmZEdOc9TlP9BEPMB47iZOhapu6WiKbQZZShEeYEnLDz%2Bu5DdQEOYEfgqcTSjHXhJvYydgd623RsMhf6QwXBlLkRyBzI2w6Tu0cTuEy%2BkHGGgPnXgPV9IaMo8sCCD3tK072QwzdDgxiVcAYl5QqjN1YrsvhiUaW%2F3MfB8S8aMwtYcV5VuHpxV%2BWPbMx"
	u := "https://release.mobio.vn/adm/api/v2.1/merchants/57d559c1-39a1-4cee-b024-b953428b5ac8/public-configs"

	// Try hybrid approach (Go + Node.js)
	fmt.Println("Trying hybrid approach (Go + Node.js)...")
	result, err := decryptPemHybrid(encryptedData, u)
	if err != nil {
		fmt.Printf("Hybrid approach failed: %v\n", err)
		fmt.Println("Make sure Node.js and crypto-es package are installed:")
		fmt.Println("  npm install crypto-es")
		fmt.Println()
	} else {
		fmt.Printf("Decrypted result: %s\n", result)

		expected := "login:vZ/OLU@.$io?SmB>uU*bQ.g{MWmhS^UF:87737cb2faf2045f5c068928ae4aedb8:<ig>CZ%K,0*S5$T1QfwUn/umjb,%[i:4dn&TPy%@6(-izrh0NlUV*AWv3k>7lSE"
		fmt.Printf("Expected result: %s\n", expected)
		fmt.Printf("Match: %t\n", result == expected)

		if result == expected {
			fmt.Println("✅ SUCCESS! Hybrid approach works correctly!")
		}
	}

	fmt.Println()
	fmt.Println("=== SOLUTION SUMMARY ===")
	fmt.Println("The hybrid approach (Go + Node.js) is the most reliable solution because:")
	fmt.Println("1. Go handles the business logic (URL decode, JWT parsing, MD5 hashing)")
	fmt.Println("2. Node.js handles the CryptoJS-specific decryption")
	fmt.Println("3. This ensures 100% compatibility with the original JavaScript behavior")
	fmt.Println()
	fmt.Println("For production use, you can:")
	fmt.Println("- Package the Node.js script as part of your Go application")
	fmt.Println("- Use a JavaScript engine embedded in Go (like goja)")
	fmt.Println("- Or find/create a Go library that exactly replicates CryptoJS behavior")
}
