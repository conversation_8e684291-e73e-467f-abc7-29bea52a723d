package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net/url"
	"strings"
)

// decryptPem is the Go equivalent of the JavaScript decryptPem function
func decryptPem(encryptedData, u string) (string, error) {
	// Step 1: URL decode the encrypted data
	decodedData, err := url.QueryUnescape(encryptedData)
	if err != nil {
		return "", fmt.Errorf("failed to URL decode: %v", err)
	}
	fmt.Println("Decoded data:", decodedData)

	// Step 2: JWT token (hardcoded as in the original JS)
	j := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5S87kehv0W3NMQRh4CIG5s3yjsQ0tUpXw5NLLSc7zs0"

	// Step 3: Extract the last part after the final dot
	parts := strings.Split(j, ".")
	var lastPart string
	if len(parts) > 0 {
		lastPart = parts[len(parts)-1]
	}

	// Step 4: Generate MD5 hash and take first 24 characters
	md5Hash := md5.Sum([]byte(lastPart + u))
	k := hex.EncodeToString(md5Hash[:])[:24]
	fmt.Println("Key (k):", k)

	// Let's also try with the full 32-character hash
	kFull := hex.EncodeToString(md5Hash[:])
	fmt.Println("Full key:", kFull)

	// Step 5: Use the 24-character key (as in JavaScript)
	keyBytes, err := hex.DecodeString(k)
	if err != nil {
		return "", fmt.Errorf("failed to decode key hex: %v", err)
	}

	// Use the same bytes for IV (as in the original JS code)
	ivBytes := make([]byte, len(keyBytes))
	copy(ivBytes, keyBytes)

	fmt.Printf("Key bytes: %x (length: %d)\n", keyBytes, len(keyBytes))
	fmt.Printf("IV bytes: %x (length: %d)\n", ivBytes, len(ivBytes))

	// Step 8: Handle key and IV sizing for AES
	// CryptoJS might be using a different key derivation method
	// Let me try using the existing project's approach

	// Use the same approach as the existing code in this project
	// Convert back to hex string and use the existing helper
	keyHex := hex.EncodeToString(keyBytes)
	ivHex := hex.EncodeToString(ivBytes)

	fmt.Printf("Key hex for existing helper: %s\n", keyHex)
	fmt.Printf("IV hex for existing helper: %s\n", ivHex)

	// Use the same approach as the existing project
	keyStr, ivStr := getKeyIv(keyHex, ivHex)
	result := decrypt(decodedData, []byte(keyStr), []byte(ivStr))

	return result, nil
}

// removePKCS7Padding removes PKCS7 padding from decrypted data
func removePKCS7Padding(data []byte) []byte {
	if len(data) == 0 {
		return data
	}

	paddingLength := int(data[len(data)-1])
	if paddingLength > len(data) || paddingLength == 0 {
		return data
	}

	// Verify padding
	for i := len(data) - paddingLength; i < len(data); i++ {
		if data[i] != byte(paddingLength) {
			return data // Invalid padding, return as is
		}
	}

	return data[:len(data)-paddingLength]
}

// getKeyIv converts hex strings to byte strings (from existing project)
func getKeyIv(key, iv string) (string, string) {
	ikey, _ := hex.DecodeString(key)
	iiv, _ := hex.DecodeString(iv)
	return string(ikey), string(iiv)
}

// decrypt function (modified from existing project to handle 12-byte keys)
func decrypt(EData string, ikey []byte, iiv []byte) string {
	ciphertext, _ := base64.StdEncoding.DecodeString(EData)

	// Expand key to 16 bytes using repeating pattern (like CryptoJS might do)
	if len(ikey) < 16 {
		expandedKey := make([]byte, 16)
		for i := 0; i < 16; i++ {
			expandedKey[i] = ikey[i%len(ikey)]
		}
		ikey = expandedKey
	}

	// Expand IV to 16 bytes using repeating pattern
	if len(iiv) < 16 {
		expandedIV := make([]byte, 16)
		for i := 0; i < 16; i++ {
			expandedIV[i] = iiv[i%len(iiv)]
		}
		iiv = expandedIV
	} else if len(iiv) > 16 {
		iiv = iiv[:16]
	}

	fmt.Printf("Final expanded key: %x\n", ikey)
	fmt.Printf("Final expanded IV: %x\n", iiv)

	block, _ := aes.NewCipher(ikey)
	mode := cipher.NewCBCDecrypter(block, iiv)
	mode.CryptBlocks(ciphertext, ciphertext)
	return string(ciphertext[:len(ciphertext)-int(ciphertext[len(ciphertext)-1])])
}

func main() {
	// Test case from the JavaScript example
	encryptedData := "cRU%2BNwmZEdOc9TlP9BEPMB47iZOhapu6WiKbQZZShEeYEnLDz%2Bu5DdQEOYEfgqcTSjHXhJvYydgd623RsMhf6QwXBlLkRyBzI2w6Tu0cTuEy%2BkHGGgPnXgPV9IaMo8sCCD3tK072QwzdDgxiVcAYl5QqjN1YrsvhiUaW%2F3MfB8S8aMwtYcV5VuHpxV%2BWPbMx"
	u := "https://release.mobio.vn/adm/api/v2.1/merchants/57d559c1-39a1-4cee-b024-b953428b5ac8/public-configs"

	// Debug key generation step by step
	j := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5S87kehv0W3NMQRh4CIG5s3yjsQ0tUpXw5NLLSc7zs0"
	parts := strings.Split(j, ".")
	var lastPart string
	if len(parts) > 0 {
		lastPart = parts[len(parts)-1]
	}
	fmt.Printf("Last part of JWT: %s\n", lastPart)
	fmt.Printf("Concatenated string: %s\n", lastPart+u)

	md5Hash := md5.Sum([]byte(lastPart + u))
	k := hex.EncodeToString(md5Hash[:])[:24]
	fmt.Printf("MD5 hash (full): %s\n", hex.EncodeToString(md5Hash[:]))
	fmt.Printf("Key k (first 24 chars): %s\n", k)
	fmt.Printf("Key k length: %d\n", len(k))

	result, err := decryptPem(encryptedData, u)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("Decrypted result: %s\n", result)

	// Expected output for comparison
	expected := "login:vZ/OLU@.$io?SmB>uU*bQ.g{MWmhS^UF:87737cb2faf2045f5c068928ae4aedb8:<ig>CZ%K,0*S5$T1QfwUn/umjb,%[i:4dn&TPy%@6(-izrh0NlUV*AWv3k>7lSE"
	fmt.Printf("Expected result: %s\n", expected)
	fmt.Printf("Match: %t\n", result == expected)
}
