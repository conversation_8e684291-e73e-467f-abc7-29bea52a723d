package handlers

import (
	"api-m/common"
	"api-m/config/env"
	"api-m/config/lang"
	"api-m/heplers/caching"
	"api-m/heplers/call_module_other"
	"api-m/heplers/file"
	"api-m/heplers/utils"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"mime"
	"mime/multipart"
	"net/http"
	"net/textproto"
	"net/url"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

func autoPass(pathApi string) bool {
	if checkAutoPass, _ := regexp.MatchString("/?ngoanvt/?", pathApi); checkAutoPass {
		return true
	}
	if checkAutoPass, _ := regexp.MatchString("/?api/v[0-9]+\\.[0-9]+/ping?", pathApi); checkAutoPass {
		return true
	}

	return !env.GetApplicationConfig().SendPem
}

func autoPassPemByMerchantId(merchantId string) bool {
	merchantIdAutoPass := map[string]bool{
		"564efc46-71be-44df-b8c7-0198082b3555": true,
		"a652dc2e-d779-11ee-854f-35c328aab60a": true,
		// "972e6e1d-8891-4fdb-9d02-8a7855393298": true,
	}

	if _, ok := merchantIdAutoPass[merchantId]; ok {
		return true
	}
	return !env.GetApplicationConfig().SendPem
}

func checkActionByPathApiGuest(configAction map[string]common.ConfigActionPathGuestApi, path string, request *http.Request) map[string][]string {
	for pathConfig, configAction := range configAction {
		if check, _ := regexp.MatchString(pathConfig, path); check {
			if configAction.IsCheck {
				if configAction.IsCheckInfoByTrackingCodeWebPush {
					trackingCode := request.Header.Get("mobio-webpush-code")
					merchantId := request.Header.Get("X-Merchant-Id")
					if trackingCode == "" {
						panic("Tracking code not exist")
					}
					keyCacheInfo := fmt.Sprintf(common.KeyCacheInfoWebPushByTrackingCode, merchantId, trackingCode)
					valueCacheInfoByTrackingCode := caching.GetValueByKey(keyCacheInfo, false, false)
					var infoByTrackingCode map[string]interface{}
					if !utils.IsValueEmpty(valueCacheInfoByTrackingCode) {
						infoByTrackingCode = valueCacheInfoByTrackingCode.(map[string]interface{})
					} else {
						infoByTrackingCode = call_module_other.GetInfoWebPushByTrackingCode(merchantId, trackingCode)
					}
					statusConfig := infoByTrackingCode["status"].(string)

					if statusConfig != "ENABLE" {
						panic("Config webpush not status ENABLE")
					}
					// request.Header.Set("X-Merchant-ID", merchantId)
					if configAction.IsVerifyToken {
						verifyTokenApiGuest(request, configAction.IsAccessTokenExpired)
					}
					if configAction.IsAutoFilDataResponse {
						dataInjectResponse := map[string][]string{
							"list_module_use": {"onpage-journey"},
						}
						return dataInjectResponse
					}

				}
			}
			return nil
		}

	}
	panic(lang.GetMessageByKey(common.UnAuthorization, request))
	// return nil
}

func checkApiAcceptedByPath(pathApi string, arrPath []string) bool {
	for _, config := range arrPath {
		if check, _ := regexp.MatchString(config, pathApi); check {
			return true
		}
	}
	return false
}

func checkExcludeFileApiByPath(pathApi string) bool {
	for _, config := range common.ExcludeCheckFileTypeApis() {
		if check, _ := regexp.MatchString(config, pathApi); check {
			return true
		}
	}
	return false
}

func getServiceHostFromPath(path string) string {
	for _, config := range common.GetServiceMap() {
		if check, _ := regexp.MatchString(config.Location, path); check {
			return strings.Join([]string{config.ServiceName, "host"}, "-")
		}
	}
	return ""
}

func checkApiBlackListByPath(merchantId string, pathApi string, method string) bool {
	merchantCode := call_module_other.GetMerchantCodeByMerchantId(merchantId)
	arrayApiBlackList := common.PathApiBlackListByMerchantCode(merchantCode, method)
	for _, config := range arrayApiBlackList {
		if check, _ := regexp.MatchString(config, pathApi); check {
			return true
		}
	}
	return false
}

type CallbackResponse struct {
	RedirectURL string `json:"redirect_url"` // Trường "redirect_url" sẽ được map với key tương ứng trong JSON
}

func processForwardApi(w http.ResponseWriter, r *http.Request, typeForward string, dataInjectResponse map[string][]string) {
	sessionRequest := r.Header.Get("mobio-session-api-m")
	timeStart := time.Now()
	body, contentType := handleBody(r)
	common.LogConfig.Infof("contentType :: %v", contentType)

	XMerchantId := r.Header.Get(common.HeaderXMerchantId)
	path := r.URL.Path
	realApi := path
	if r.URL.RawQuery != "" {
		realApi += "?" + r.URL.RawQuery
	}
	common.LogConfig.Infof("[%v] :: GetServiceHostFromPath :: path :: %v", sessionRequest, path)
	serviceHost := getServiceHostFromPath(path)
	common.LogConfig.Infof("[%v] :: GetServiceHostFromPath :: serviceHost :: %v", sessionRequest, serviceHost)
	urlOrigin := generateUrl(serviceHost, XMerchantId, realApi, sessionRequest)
	if urlOrigin == "" {
		panic(lang.StructureMessage{Code: http.StatusServiceUnavailable, Message: "Service Unavailable"})
	}
	proxyReq, err := http.NewRequest(r.Method, urlOrigin, body)
	if proxyReq == nil {
		common.LogConfig.Errorf("[%v] :: proxyReq is nil", sessionRequest)
		panic(lang.StructureMessage{Code: http.StatusNoContent, Message: err.Error()})
	}
	for key, values := range r.Header {
		for _, value := range values {
			proxyReq.Header.Set(key, value)
		}
	}

	proxyReq.Header.Set(common.HeaderContentType, contentType)

	// Thêm header cho đầu mobile để thực hiện check abac
	if typeForward == common.TypeForwardPublicPu {
		proxyReq.Header.Set(common.MobioDeviceType, common.MobileDeviceType)
	}

	t := http.DefaultTransport.(*http.Transport).Clone()
	t.IdleConnTimeout = time.Millisecond * 100
	c := &http.Client{
		Transport: t,
	}

	res, err := c.Do(proxyReq)
	if err != nil {
		common.LogConfig.Errorf("[%v] :: Do proxyReq error :: %v", sessionRequest, err.Error())
		panic(lang.StructureMessage{Code: http.StatusNoContent, Message: err.Error()})
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			common.LogConfig.Errorf("[%v] :: Do proxyReq error :: %v", sessionRequest, err.Error())
		}
	}(res.Body)
	resBody, err := ioutil.ReadAll(res.Body)
	if err != nil {
		common.LogConfig.Errorf("[%v] :: Read body :: %v", sessionRequest, err.Error())
		panic(lang.StructureMessage{Code: http.StatusInternalServerError, Message: err.Error()})
	}
	for key, values := range res.Header {
		if key == "Content-Length" {
			continue
		}
		for _, value := range values {
			w.Header().Set(key, value)
		}
	}
	resultCheckPathRedirect := checkApiAcceptedByPath(path, common.PathApiRedirect())
	if resultCheckPathRedirect {
		var callbackResp CallbackResponse
		if err := json.Unmarshal(resBody, &callbackResp); err != nil {
			http.Error(w, "Failed to parse response from Python API", http.StatusInternalServerError)
			return
		}
		// Redirect đến URL được trả về từ Python API
		if callbackResp.RedirectURL != "" {
			common.LogConfig.Infof("callbackResp.RedirectURL :: %v", callbackResp.RedirectURL)
			http.Redirect(w, r, callbackResp.RedirectURL, http.StatusTemporaryRedirect)
		} else {
			http.Error(w, "No redirect URL provided", http.StatusBadRequest)
		}
		return

	}
	w.WriteHeader(res.StatusCode)

	if dataInjectResponse != nil {

		// Kiểm tra tính hợp lệ của dữ liệu JSON
		if !json.Valid(resBody) {
			// Nếu không phải là JSON hợp lệ, ghi dữ liệu gốc và thoát
			_, err := w.Write(resBody)
			if err != nil {
				common.LogConfig.Errorf("[%v] :: Write body error :: %v", sessionRequest, err.Error())
			}
			return
		}
		// Khai báo biến newResBody để chứa dữ liệu mới
		var newResBody []byte

		// Tạo một map mới từ resBody
		var data map[string]interface{}
		err := json.Unmarshal(resBody, &data)
		if err != nil {
			common.LogConfig.Errorf("[%v] :: Unmarshal response body :: %v", sessionRequest, err.Error())
			panic(lang.StructureMessage{Code: http.StatusInternalServerError, Message: err.Error()})
		}

		// Thêm dữ liệu mới vào map
		data["list_module_use"] = []string{"onpage-journey"}

		// Chuyển đổi map thành JSON
		newResBody, err = json.Marshal(data)
		if err != nil {
			common.LogConfig.Errorf("[%v] :: Marshal response body :: %v", sessionRequest, err.Error())
			panic(lang.StructureMessage{Code: http.StatusInternalServerError, Message: err.Error()})
		}
		_, err = w.Write(newResBody)
		if err != nil {
			common.LogConfig.Errorf("[%v] :: Write body error :: %v", sessionRequest, err.Error())
		}
	} else {
		// Gửi resBody gốc nếu không có dữ liệu mới để thêm vào
		_, err := w.Write(resBody)
		if err != nil {
			common.LogConfig.Errorf("[%v] :: Write body error :: %v", sessionRequest, err.Error())
		}
	}

	common.LogConfig.Infof("[%v] :: Time process forward :: %v", sessionRequest, time.Since(timeStart))
}

func deletePemUrl(rawQuery string) string {
	reXPem := regexp.MustCompile(`(&?x-pem=[^&\r\n]*)(&|$)`)
	output := reXPem.ReplaceAllString(rawQuery, "$2")

	rePem := regexp.MustCompile(`(&?pem=[^&\r\n]*)(&|$)`)
	result := rePem.ReplaceAllString(output, "$2")

	result = strings.ReplaceAll(result, "&&", "&")
	if strings.HasPrefix(result, "&") {
		result = strings.Replace(result, "&", "", 1)
	}
	return result
}

func generateUrl(serviceHost string, merchantId string, realApi string, sessionRequest string) string {
	// regexAutoPass := regexp.MustCompile(`/?ngoanvt/?`)
	// realApi = regexAutoPass.ReplaceAllString(realApi, "")
	hostServiceMesh := call_module_other.GetMerchantConfigHost(merchantId, serviceHost)
	if hostServiceMesh == "" {
		hostServiceMesh = fmt.Sprintf("%v://%v/", "http", strings.Replace(serviceHost, "-host", ".mobio", 1))
		return ""
	}
	if strings.ToLower(env.GetApplicationConfig().VmType) == "local" {
		// hostServiceMesh = "https://api-test1.mobio.vn/"
		hostServiceMesh = "http://127.0.0.1:5009/"
		//hostServiceMesh = "http://127.0.0.1:5008/"
	}

	common.LogConfig.Infof("[%v] :: generateUrl :: hostServiceMesh :: %v", sessionRequest, hostServiceMesh)
	realPathParse, _ := url.Parse(realApi)
	realPathParse.RawQuery = deletePemUrl(realPathParse.RawQuery)
	realApi = realPathParse.String()
	realApi = strings.Replace(realApi, "./", "", 1)
	if strings.HasPrefix(realApi, "/") {
		realApi = strings.Replace(realApi, "/", "", 1)
	}
	//realApi = strings.ReplaceAll(realApi, "//", "/")
	// Convert lại cho đúng param
	realApi = strings.ReplaceAll(realApi, common.MobioSpecialSemicolons, ";")
	common.LogConfig.Infof("[%v] :: generateUrl :: realApi :: %v", sessionRequest, realApi)
	resultUrl := strings.Join([]string{hostServiceMesh.(string), realApi}, "")
	common.LogConfig.Infof("[%v] :: generateUrl :: resultUrl :: %v", sessionRequest, resultUrl)
	return resultUrl
}

func checkIsFormData(header http.Header) bool {
	for key, values := range header {
		for _, value := range values {
			if strings.ToLower(key) == "content-type" {
				if check := strings.Contains(value, "form-"); check {
					return true
				}
			}
		}
	}
	return false
}

var quoteEscaper = strings.NewReplacer("\\", "\\\\", `"`, "\\\"")

func escapeQuotes(s string) string {
	return quoteEscaper.Replace(s)
}

func buildFilename(fileName string, contentType string) (string, error) {
	extensionFilename := filepath.Ext(fileName)
	isValid := false
	if contentType == "application/zip" {
		for _, value := range file.ExtensionDocumentAllow {
			if "."+value == extensionFilename {
				isValid = true
				break
			}
		}
	}
	fileNameNotExt := strings.TrimSuffix(fileName, extensionFilename)
	contentTypeByExt := mime.TypeByExtension(extensionFilename)
	if contentType != contentTypeByExt && !isValid {
		extension, err := mime.ExtensionsByType(contentType)
		if err == nil {
			return fmt.Sprintf("%s%s", fileNameNotExt, extension[0]), nil
		}
		return fmt.Sprintf("%s%s", fileNameNotExt, extensionFilename), nil
	}
	return fileName, nil

}

func handleBody(r *http.Request) (*bytes.Buffer, string) {
	sessionRequest := r.Header.Get("mobio-session-api-m")
	common.LogConfig.Infof("[%v] :: Start handleBody", sessionRequest)
	body := &bytes.Buffer{}
	var contentType string
	if checkIsFormData(r.Header) {
		_ = r.ParseMultipartForm(common.MaxMemoryUpload)
		newBody := &bytes.Buffer{}
		writer := multipart.NewWriter(newBody)
		defer func(writer *multipart.Writer) {
			err := writer.Close()
			if err != nil {
				common.LogConfig.Errorf("[%v] :: handleBody :: %v", sessionRequest, err)
			}
		}(writer)

		multipartFormFile := r.MultipartForm

		if multipartFormFile != nil {
			for keys, values := range multipartFormFile.File {
				for _, value := range values {
					f, _ := value.Open()
					buff := bytes.NewBuffer(nil)
					_, err := io.Copy(buff, f)
					if err != nil {
						return nil, ""
					}
					isValid, contentType := file.ValidateExtensionFileUploadInAllowType(buff.Bytes())
					common.LogConfig.Infof("[%v] :: multipartFormFile :: contentType :: %v", sessionRequest, contentType)
					if isValid || checkExcludeFileApiByPath(r.URL.Path) {
						// custom content-type
						filenameUpload := escapeQuotes(value.Filename)
						filenameFw, _ := buildFilename(filenameUpload, contentType)
						h := make(textproto.MIMEHeader)
						h.Set("Content-Disposition",
							fmt.Sprintf(`form-data; name="%s"; filename="%s"`,
								escapeQuotes(keys), filenameFw))
						h.Set("Content-Type", contentType)
						w, _ := writer.CreatePart(h)

						_, err2 := f.Seek(0, io.SeekStart)
						if err2 != nil {
							common.LogConfig.Errorf("[%v] :: handleBody :: %v", sessionRequest, err2)
							return nil, ""
						}
						_, err := io.Copy(w, f)
						if err != nil {
							common.LogConfig.Errorf("[%v] :: handleBody :: %v", sessionRequest, err)
						}
					}
					err = f.Close()
					if err != nil {
						common.LogConfig.Errorf("[%v] :: handleBody :: %v", sessionRequest, err)
					}
				}
			}
		}

		formFile := r.PostForm
		// write value
		if formFile != nil {
			for keys, values := range formFile {
				for _, value := range values {
					w, e := writer.CreateFormField(keys)
					if e == nil {
						_, err := w.Write([]byte(value))
						if err != nil {
							common.LogConfig.Errorf("[%v] :: handleBody :: %v", sessionRequest, err)
							return nil, ""
						}
					}
				}
			}
		}

		if multipartFormFile != nil {
			for keys, values := range multipartFormFile.Value {
				for _, value := range values {
					w, e := writer.CreateFormField(keys)
					if e == nil {
						_, err := w.Write([]byte(value))
						if err != nil {
							common.LogConfig.Errorf("[%v] :: handleBody :: %v", sessionRequest, err)
							return nil, ""
						}
					}
				}
			}
		}

		body = newBody
		contentType = writer.FormDataContentType()

	} else {
		readBody, _ := ioutil.ReadAll(r.Body)
		body = bytes.NewBuffer(readBody)
		contentType = r.Header.Get(common.HeaderContentType)
	}
	return body, contentType
}

func checkSpecialApiIfHas(r *http.Request) {
	pathApi := r.URL.Path
	common.LogConfig.Infof("check_special_api_if_has():api: %s", pathApi)
	if matched, _ := regexp.MatchString(`^/?adm/api/v[0-9.]+/merchants/sub-brands/[0-9A-z._-]+/accounts/[0-9A-z._-]+$`, pathApi); matched {
		_ = r.ParseMultipartForm(common.MaxMemoryUpload)
		newBody := &bytes.Buffer{}
		writer := multipart.NewWriter(newBody)
		defer func(writer *multipart.Writer) {
			err := writer.Close()
			if err != nil {
				common.LogConfig.Errorf("handleBody :: %v", err)
			}
		}(writer)

		requestForm := r.Form
		requestMultiForm := r.MultipartForm

		accountInfo := make(map[string]interface{})
		if requestForm != nil {
			if info, ok := requestForm["info"]; ok {
				if err := json.Unmarshal([]byte(info[0]), &accountInfo); err != nil {
					common.LogConfig.Errorf("authorization_controller::check_special_api_if_has():%v", err)
					return
				}
			}
		} else if requestMultiForm != nil {
			if info, ok := requestMultiForm.Value["info"]; ok {
				if err := json.Unmarshal([]byte(info[0]), &accountInfo); err != nil {
					common.LogConfig.Errorf("authorization_controller::check_special_api_if_has():%v", err)
					return
				}
			}
		} else {
			return
		}

		newRoleGroup := accountInfo["role_group"].(string)
		oldRoleGroupFromJWT := GetValueInTokenFromRequest(r, "role_group")
		if oldRoleGroupFromJWT == nil {
			oldRoleGroupFromJWT = "admin"
		}
		switch oldRoleGroupFromJWT {
		case "user":
			if newRoleGroup == "manager" || newRoleGroup == "admin" || newRoleGroup == "owner" {
				panic(lang.GetMessageByKey(common.UnAuthorization, r))
			}
		case "manager":
			if newRoleGroup == "admin" || newRoleGroup == "owner" {
				panic(lang.GetMessageByKey(common.UnAuthorization, r))
			}
		case "admin":
			if newRoleGroup == "owner" {
				panic(lang.GetMessageByKey(common.UnAuthorization, r))
			}
		}
	}
}

func checkFixResponseApiIfHas(pathApi string) (bool, string) {
	vmType := env.GetApplicationConfig().VmType
	config := map[string]map[string]string{
		"PVCB": {
			`^/?dynamic-event/api/v[0-9\\.]+/profile/filter/[0-9A-z._-]+/events$`: `{"code":200,"lang":"vi","message":"Request Success!","data":[],"paging":{"cursors":{"after":"","before":None},"per_page":10,"total_items":0}}`,
		},
	}
	values, err := config[vmType]
	if !err {
		common.LogConfig.Debugf("checkFixResponseApiIfHas :: %v", err)
		return false, ""
	}
	for pA, res := range values {
		if check, _ := regexp.MatchString(pA, pathApi); check {
			return true, res
		}
	}
	return false, ""

}
