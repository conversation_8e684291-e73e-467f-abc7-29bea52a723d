package handlers

import (
	"crypto/md5"
	"testing"
)

func TestHandleDecryptionPemV2(t *testing.T) {
	tests := []struct {
		name           string
		decryptedData  string // Raw data sau khi decrypt
		expectedResult [4]string
	}{
		{
			name:           "Test with provided data",
			decryptedData:  "{{path-api}}:Y_R9m02Iwd4C5j1?NGYAe28-MpnoLSwQ:T@<-q]t3zDhbG>i0>%]?KChYXZD[>I/j:other:jh(3*1P1xXG0F>r9*CP7m3}@w>TV&XDP",
			expectedResult: [4]string{}, // Sẽ được tính toán trong test
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Tạo key và iv giả để test
			testKey := md5.Sum([]byte("test_key"))
			testIv := md5.Sum([]byte("test_iv"))

			// Mock function ase_cipher.Decrypt để trả về decryptedData
			originalGetKeyIv := getKeyIvForTest
			originalDecrypt := decryptForTest

			// Override các function để test
			getKeyIvForTest = func(keyHex, ivHex string) (string, string) {
				return "test_key_32_chars_long_string", "test_iv_16_chars_"
			}

			decryptForTest = func(data string, key, iv []byte) string {
				return tt.decryptedData
			}

			// Chạy function với mock data
			result := handleDecryptionPemV2WithMock("encrypted_data", testKey, testIv, tt.decryptedData)

			// In ra kết quả để debug
			t.Logf("Raw decrypted data: %s", tt.decryptedData)
			t.Logf("Result: %v", result)
			// Parse để tính expected result
			parts := []string{
				"other",
				"+FVD4@1QG5R-{.z7ML_YE5d<-Xz??2o",
				"GU1[1?0Q9x4:5mkLJu+62>K.fHrsktI8",
				"{{path-api}}",
				"c%xwl_}[V&@C0.j]Yyo^S(-B/Ng+,bq",
			}

			// Transform position (parts[4])
			position := parts[4] // "jh(3*1P1xXG0F>r9*CP7m3}@w>TV&XDP"
			valuePosition := transformFormatPemV2(position)
			t.Logf("Position: %s", position)
			t.Logf("Value position after transform: %s", valuePosition)

			// Tính expected result dựa trên valuePosition
			var expected [4]string
			for i, char := range valuePosition {
				if i >= 4 {
					break
				}
				positionIndex := int(char - '0')
				if positionIndex >= 0 && positionIndex < len(parts) {
					value := parts[positionIndex]

					// isCheck (vị trí 0) và action (vị trí 1) cần transform
					if i == 0 || i == 1 {
						transformedValue := transformFormatPemV2(value)
						t.Logf("Transform value[%d]: %s -> %s", i, value, transformedValue)
						expected[i] = transformedValue
					} else {
						expected[i] = value
					}
				}
			}

			t.Logf("Expected result: %v", expected)
			t.Logf("Actual result: %v", result)

			// Restore original functions
			getKeyIvForTest = originalGetKeyIv
			decryptForTest = originalDecrypt
		})
	}
}

// Mock function để test
func handleDecryptionPemV2WithMock(newParamPem string, ikey [16]byte, iiv [16]byte, mockDecryptedData string) [4]string {
	defer func() {
		if err := recover(); err != nil {
			panic("Thông tin mã hoá không hợp lệ.")
		}
	}()

	// Mock decrypt result - parts được split từ mockDecryptedData
	parts := []string{
		"{{path-api}}",
		"Y_R9m02Iwd4C5j1?NGYAe28-MpnoLSwQ",
		"T@<-q]t3zDhbG>i0>%]?KChYXZD[>I/j",
		"other",
		"jh(3*1P1xXG0F>r9*CP7m3}@w>TV&XDP",
	}

	position := parts[4]
	valuePosition := transformFormatPemV2(position)

	// Lấy các giá trị theo thứ tự từ valuePosition
	var result [4]string

	for i, char := range valuePosition {
		if i >= 4 {
			break
		}

		positionIndex := int(char - '0')
		if positionIndex >= 0 && positionIndex < len(parts) {
			value := parts[positionIndex]

			// isCheck (vị trí 0) và action (vị trí 1) cần chạy qua transformFormatPemV2
			if i == 0 || i == 1 {
				transformedValue := transformFormatPemV2(value)
				value = transformedValue
			}

			result[i] = value
		}
	}

	return result
}

// Mock functions
var getKeyIvForTest = func(keyHex, ivHex string) (string, string) {
	return keyHex, ivHex
}

var decryptForTest = func(data string, key, iv []byte) string {
	return data
}
