package handlers

import (
	"api-m/common"
	"api-m/heplers/logger"
	"net/http"
)

func Web(w http.ResponseWriter, r *http.Request) {
	pathApi := r.URL.Path
	method := r.Method
	merchantIdHeader := r.Header.Get("X-Merchant-Id")

	resultCheckBlackList := checkApiBlackListByPath(merchantIdHeader, pathApi, method)
	if resultCheckBlackList {
		logger.ConfigLogger().Info("Path api in blacklist")
		panic("Bạn không có quyền truy cập chức năng này!")
	}

	checkSpecialApiIfHas(r)

	resultCheckPathRedirect := checkApiAcceptedByPath(pathApi, common.PathApiRedirect())
	if !autoPass(pathApi) && !checkApiAcceptedByPath(pathApi, common.PathApiExclude()) && !autoPassPemByMerchantId(merchantIdHeader) && !resultCheckPathRedirect {
		authType := VerifyMerchant(r)
		pathSpecial, responseFix := checkFixResponseApiIfHas(pathApi)
		if pathSpecial {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			_, err := w.Write([]byte(responseFix))
			if err != nil {
				panic(err)
			}
			return
		}
		if authType != common.TypicallyBasic {
			CheckPermission(r, common.PrefixPathWeb)
		}
	}
	processForwardApi(w, r, common.TypeForwardWeb, nil)
}
