package handlers

import (
	"api-m/common"
	"api-m/config/lang"
	"api-m/heplers/respone"
	"crypto/md5"
	"net/http"
)

func DecodePemService(w http.ResponseWriter, r *http.Request) {
	_, _, singKey, _ := getItemTokenByHeader("", r)
	paramPem := r.URL.Query().Get("pem")
	if paramPem == "" {
		paramPem = r.URL.Query().Get("x-pem")
	}
	if paramPem == "" {
		panic(lang.GetMessageByKey(common.PemError, r))
	}
	
	paramPem = unquotePemParam(paramPem)
	numberItem := len(paramPem) % 4
	newParamPem := paramPem
	for i := 0; i < numberItem; i++ {
		newParamPem += "="
	}
	ikey := md5.Sum([]byte(common.AesCipherKey + singKey))
	iiv := md5.Sum([]byte(common.AesCipherIv + singKey))

	parts := handleDecryptionPem(newParamPem, ikey, iiv)
	result := map[string][4]string{
		"decode": parts,
	}
	response := respone.Response{Code: http.StatusOK, Message: "request success!", Data: result, ResponseWriter: w}
	response.BuildResponseJson()
}
