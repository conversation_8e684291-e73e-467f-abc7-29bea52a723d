package handlers

import (
	"api-m/common"
	"api-m/config/env"
	"api-m/config/lang"
	"api-m/config/url_api_other"
	"api-m/heplers/ase_cipher"
	"api-m/heplers/caching"
	"api-m/heplers/call_module_other"
	"api-m/heplers/utils"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/golang-jwt/jwt"
)

func genToken(tokenStr string) (map[string]interface{}, error) {
	claims := jwt.MapClaims{}
	jwt.ParseWithClaims(tokenStr, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			panic("unexpected signing method")
		}
		return nil, nil
	})

	resultToken := map[string]interface{}{}

	for key, val := range claims {
		resultToken[key] = val
	}
	return resultToken, nil
}

func getValueByKeyInToken(tokenStr string, key string) interface{} {
	tokenData, err := genToken(tokenStr)
	if err != nil {
		return nil
	}
	return tokenData[key]
}

func GetValueInTokenFromRequest(r *http.Request, key string) any {
	InputHeaders := r.Header
	AuthenticateData := InputHeaders.Get("Authorization")
	_, tokenData, _, _ := getItemTokenByHeader(AuthenticateData, r)
	token, err := genToken(tokenData)
	if err != nil {
		return nil
	}
	return token[key]
}

func getItemTokenByHeader(authenticateData string, req *http.Request) (string, string, string, string) {
	if authenticateData == "" {
		authenticateData = req.Header.Get("Authorization")
	}
	splitAuthenticateData := strings.Split(authenticateData, " ")
	authType, tokenData := splitAuthenticateData[0], splitAuthenticateData[1]
	splitToken := strings.Split(tokenData, ".")
	signature := ""
	if authType == common.TypicallyBearer {
		signature = splitToken[2]
	}
	return authType, tokenData, signature, authenticateData
}

func VerifyMerchant(req *http.Request) string {
	timeStart := time.Now()
	inputHeaders := req.Header
	authenticateData := inputHeaders.Get("Authorization")
	if authenticateData == "" {
		panic("Không tìm thấy token")
	}
	XMerchantIDFromHeader := inputHeaders.Get("X-Merchant-ID")
	if XMerchantIDFromHeader == "" {
		panic(lang.GetMessageByKey(common.UnAuthentice, req))
	}

	authType, tokenData, singKey, _ := getItemTokenByHeader(authenticateData, req)
	if authType == common.TypicallyBearer {
		xMerchantIDFromToken := getValueByKeyInToken(tokenData, "merchant_id")
		if XMerchantIDFromHeader != xMerchantIDFromToken || !checkEstimateToken(tokenData) {
			panic(lang.GetMessageByKey(common.UnAuthentice, req))
		}
		accountIdToken := getValueByKeyInToken(tokenData, "id")
		deviceTypeToken := getValueByKeyInToken(tokenData, "device_type")
		accountId, okAccount := accountIdToken.(string)
		deviceType, okDevice := deviceTypeToken.(string)
		merchantId, okMerchant := xMerchantIDFromToken.(string)
		if !okDevice {
			deviceType = "web"
		}
		if okAccount && okMerchant {
			saveCacheJWTCallAPI(merchantId, accountId, singKey, deviceType)
		}
	}
	if authType == common.TypicallyBasic {
		// if !checkBasicAuth(tokenData, XMerchantIDFromHeader) {
		// 	panic(lang.GetMessageByKey(common.UnAuthentice, req))
		// }
		checkBasicAuth(tokenData, XMerchantIDFromHeader, req)
	}
	common.LogConfig.Infof("Time process VerifyMerchant :: %v", time.Since(timeStart))
	return authType
}

func checkEstimateToken(token string) bool {
	t1 := time.Now()
	splitToken := strings.Split(token, ".")
	signature := splitToken[2]
	valueRedis := caching.GetValueByKey(signature, false, true)
	common.LogConfig.Infof("Time process checkEstimateToken :: %v", time.Since(t1))
	return !utils.IsValueEmpty(valueRedis)
}

func checkBasicAuth(token, merchantIdFromHeader string, request *http.Request) {
	// Phần này sẽ call sang api để check basic token còn hạn hay không?
	// Nếu còn hạn thì sẽ thực hiện lưu cache 1 tiếng
	// Key lưu cach: CheckBasicAuth#<token>#<merchant_id>
	keyCache := strings.Join([]string{"CheckBasicAuth", token, merchantIdFromHeader}, "#")
	value := caching.GetValueByKey(keyCache, true, true)
	if value != "" {
		return
	}
	adminUrl := fmt.Sprintf(url_api_other.CheckBasicTokenAuth, env.GetApplicationConfig().AdminHost, token)
	req, err := http.NewRequest(http.MethodGet, adminUrl, nil)
	if err != nil {
		panic(lang.GetMessageByKey(common.UnAuthentice, request))
	}
	res, err := http.DefaultClient.Do(req)
	if err != nil {
		panic(lang.GetMessageByKey(common.UnAuthentice, request))
	}
	if res.StatusCode == http.StatusOK {
		var result map[string]interface{}
		json.NewDecoder(res.Body).Decode(&result)
		data := result["data"].(map[string]interface{})
		merchantIdRes := data["merchant_id"].(string)
		expired := data["expired"].(bool)
		if !utils.IsValueEmpty(merchantIdRes) && merchantIdRes != merchantIdFromHeader {
			panic(lang.GetMessageByKey(common.UnAuthorization, request))
		}
		if expired {
			panic(lang.GetMessageByKey(common.UnAuthentice, request))
		}
		caching.SetKeyByValue(keyCache, "OK", 1*time.Hour, true)
		return
	}
	panic(lang.GetMessageByKey(common.UnAuthentice, request))
}

func getAllPermissionByUser(merchantId string, userId string, token string) []interface{} {
	common.LogConfig.Infof("getAllPermissionByUser :: merchant_id : %v, userId : %v", merchantId, userId)
	t1 := time.Now()
	// keyCache := caching.BuildKeyCacheByInput("getAllPermissionByUser", merchantId, userId)
	keyCache := fmt.Sprintf("apim#get_all_permission_by_user#(('{%v}', '{%v}'), {})", merchantId, userId)
	valueCache := caching.GetValueByKey(keyCache, false, true)
	common.LogConfig.Infof("Time process get key getAllPermissionByUser :: %v", time.Since(t1))
	if !utils.IsValueEmpty(valueCache) {
		return valueCache.([]interface{})
	}
	adminUrl := fmt.Sprintf(url_api_other.AccountFunctions, env.GetApplicationConfig().AdminHost)
	common.LogConfig.Infof("AdminUrl :: %v", adminUrl)
	req, err := http.NewRequest(http.MethodGet, adminUrl, nil)
	req.Header.Set("X-Merchant-ID", merchantId)
	req.Header.Set("Authorization", token)
	req.Header.Set("Content-type", "application/json")
	if err != nil {
		return []interface{}{}
	}
	res, err := http.DefaultClient.Do(req)
	common.LogConfig.Infof("res :: %v", res)
	if err != nil {
		return []interface{}{}
	}
	if res.StatusCode == http.StatusOK {
		var result map[string]interface{}
		json.NewDecoder(res.Body).Decode(&result)
		accountFunctions := result["data"].([]interface{})
		caching.SetKeyByValue(keyCache, accountFunctions, time.Duration(env.ConfigEnvRedis().TimeSecondCachePermission)*time.Second, false)
		return accountFunctions
	}
	return []interface{}{}
}

func checkAction(merchantId string, authenticate string, userId string, path string, action string) bool {
	t1 := time.Now()
	accountFunctions := getAllPermissionByUser(merchantId, userId, authenticate)
	common.LogConfig.Infof("Time process getAllPermissionByUser :: %v", time.Since(t1))
	if len(accountFunctions) == 0 {
		return false
	}
	hasPermission := false
	for _, permission := range accountFunctions {
		convertPermission := permission.(map[string]interface{})
		actions := convertPermission["actions"].([]interface{})
		newPath := convertPermission["path"]
		newPath = newPath.(string)
		if newPath == path && len(actions) > 0 {
			for _, v := range actions {
				vAction, _ := strconv.Atoi(action)
				if int(v.(float64)) == vAction {
					hasPermission = true
					break
				}
			}
		}
		if hasPermission {
			break
		}
	}
	common.LogConfig.Debugf("Time process checkAction :: %v", time.Since(t1))
	return hasPermission
}

func CheckPermission(req *http.Request, prefix string) {
	common.LogConfig.Infof("Start check permission")
	timeStart := time.Now()
	paramPem := req.URL.Query().Get("pem")
	// if paramPem == "" {
	// 	paramPem = req.URL.Query().Get("x-pem")
	// }
	if paramPem == "" {
		paramPem = req.Header.Get("mobio-pem")
	}
	if paramPem == "" {
		panic(lang.GetMessageByKey(common.PemError, req))
	}

	versionClient := versionCheckPemFromClient(req)
	switch versionClient {
	case common.VersionOne:
		decryptionPem(paramPem, req, prefix)
	case common.VersionTwo:
		decryptionPemV2(paramPem, req, prefix)
	default:
		decryptionPem(paramPem, req, prefix)
	}

	common.LogConfig.Debugf("Time process CheckPermission :: %v", time.Since(timeStart))
}

func unquotePemParam(paramPem string) string {
	result := paramPem
	i := 0
	for {
		i++
		result, _ = url.QueryUnescape(result)
		if !strings.Contains(result, "%") || i >= 5 {
			break
		}

	}
	return strings.ReplaceAll(result, " ", "+")
}

func handleDecryptionPem(newParamPem string, ikey [16]byte, iiv [16]byte) [4]string {

	defer func() {
		if err := recover(); err != nil {
			panic("Thông tin mã hoá không hợp lệ.")
		}
	}()
	key, iv := ase_cipher.GetKeyIv(hex.EncodeToString(ikey[:]), hex.EncodeToString(iiv[:]))
	decryptionPem := ase_cipher.Decrypt(newParamPem, []byte(key), []byte(iv))
	parts := strings.Split(decryptionPem, ":")
	if len(parts) < 4 {
		// Thử giải mã theo key cũ
		key, iv := ase_cipher.GetKeyIv(common.AesCipherKey, common.AesCipherIv)
		decryptionPem := ase_cipher.Decrypt(newParamPem, []byte(key), []byte(iv))
		parts = strings.Split(decryptionPem, ":")
	}
	return [4]string{parts[0], parts[1], parts[2], parts[3]}
}

func transformFormatPemV2(value string) string {
	// Map tương ứng với JS: {'0': 0, '@': 1, '&': 2, '(':3,')': 4 ,'!': 5,'~': 6 , '`':7, '|':8 ,'=': 9 }
	charMap := map[string]int{
		"0": 0,
		"@": 1,
		"&": 2,
		"(": 3,
		")": 4,
		"!": 5,
		"~": 6,
		"`": 7,
		"|": 8,
		"=": 9,
	}

	// Decode logic tương ứng với JS: val.split('').filter((key:string) => key in map).map((key:string) => map[key]).join('')
	var decoded []string
	for _, char := range value {
		charStr := string(char)
		if value, exists := charMap[charStr]; exists {
			decoded = append(decoded, strconv.Itoa(value))
		}
	}
	decodedString := strings.Join(decoded, "")
	return decodedString
}

func handleDecryptionPemV2(newParamPem string, ikey [16]byte, iiv [16]byte) [4]string {
	defer func() {
		if err := recover(); err != nil {
			panic("Thông tin mã hoá không hợp lệ.")
		}
	}()

	// // TODO: Implement remaining decryption logic using decodedString
	// _ = decodedString // Temporarily unused to avoid linter error
	key, iv := ase_cipher.GetKeyIv(hex.EncodeToString(ikey[:]), hex.EncodeToString(iiv[:]))
	decryptionPem := ase_cipher.Decrypt(newParamPem, []byte(key), []byte(iv))
	parts := strings.Split(decryptionPem, ":")

	position := parts[4]
	valuePosition := transformFormatPemV2(position)

	// Lấy các giá trị theo thứ tự từ valuePosition
	// isCheck ở vị trí 0, action ở vị trí 1, pathCheck ở vị trí 2, pathApi ở vị trí 3
	var result [4]string

	for i, char := range valuePosition {
		if i >= 4 { // Chỉ lấy tối đa 4 vị trí
			break
		}

		positionIndex := int(char - '0') // Chuyển ký tự thành số
		if positionIndex >= 0 && positionIndex < len(parts) {
			value := parts[positionIndex]

			// isCheck (vị trí 0) và action (vị trí 1) cần chạy qua transformFormatPemV2
			if i == 0 || i == 1 {
				transformedValue := transformFormatPemV2(value)
				// Convert về int rồi lại về string
				if intValue, err := strconv.Atoi(transformedValue); err == nil {
					value = strconv.Itoa(intValue)
				} else {
					value = transformedValue // Fallback nếu không convert được
				}
			}

			result[i] = value
		}
	}

	return result
}

func decryptionPemV2(paramPem string, req *http.Request, prefix string) {
	sessionRequest := req.Header.Get("mobio-session-api-m")
	common.LogConfig.Infof("[%v] :: Start decryptionPemV2", sessionRequest)
	authType, token, singKey, authenticate := getItemTokenByHeader("", req)
	if authType == common.TypicallyBasic {
		return
	}
	xMerchantId := req.Header.Get("X-Merchant-Id")
	userId := getValueByKeyInToken(token, "id")
	paramPem = unquotePemParam(paramPem)
	numberItem := len(paramPem) % 4
	newParamPem := paramPem
	for i := 0; i < numberItem; i++ {
		newParamPem += "="
	}

	// Check power pem
	isCheckPowerPem := false
	if env.GetApplicationConfig().PowerPem != "" {
		powerPem := env.GetApplicationConfig().PowerPem
		powerPem = unquotePemParam(powerPem)
		numberItemPowerPem := len(powerPem) % 4
		for i := 0; i < numberItemPowerPem; i++ {
			powerPem += "="
		}
		if powerPem == newParamPem {
			isCheckPowerPem = true
		}
	}

	pathUri := req.URL.Path
	newProtocol := req.Header.Get("x-forwarded-proto")
	if newProtocol == "" {
		newProtocol = "https"
	}
	newHost := req.Host

	common.LogConfig.Infof("newHost :: %v", newHost)
	if utils.IsValueEmpty(newHost) {
		newHost = "test1.mobio.vn"
	}

	newPort := req.Header.Get("x-port")
	newSever := fmt.Sprintf("%v://%v", newProtocol, newHost)
	if newPort != "" {
		newSever += fmt.Sprintf(":%v", newPort)
	}
	if prefix != common.PrefixPathWeb {
		newSever += fmt.Sprintf("%v", prefix)
	}
	newSever += "/"
	newBaseUrl, _ := url.JoinPath(newSever, pathUri)

	// Tôi muốn lấy pathUri + singKey sau đó mã hoá theo md5 sau đó lấy 32 ký tự
	md5PathUri := md5.Sum([]byte(singKey + newBaseUrl))
	md5PathUriString := hex.EncodeToString(md5PathUri[:])
	md5PathUriString = md5PathUriString[:24]

	// Giải mã theo dạng mới
	ikey := md5.Sum([]byte(md5PathUriString))
	iiv := md5.Sum([]byte(md5PathUriString))		

	parts := handleDecryptionPemV2(newParamPem, ikey, iiv)
	isCheckPermission := parts[0]
	action := parts[1]
	checkSum := parts[2]
	path := parts[3]
	if isCheckPowerPem {
		common.LogConfig.Debugf("[%v] :: Api use powerpem", sessionRequest)
		return
	}
	if v, _ := strconv.Atoi(isCheckPermission); v > 0 {
		common.LogConfig.Infof("[%v] :: newBaseUrl :: %v", sessionRequest, newBaseUrl)
		serverSignature := md5.Sum([]byte(newBaseUrl))
		sumServerSignature := hex.EncodeToString(serverSignature[:])
		common.LogConfig.Infof("[%v] :: sumServerSignature :: %v", sessionRequest, sumServerSignature)
		common.LogConfig.Infof("[%v] :: checkSum :: %v", sessionRequest, checkSum)
		if checkSum == sumServerSignature {
			logMessage := fmt.Sprintf("[%v] :: Start checkAction-xMerchantId :: %v, authenticate :: %v, userID :: %v, path :: %v, action :: %v ",
				sessionRequest, xMerchantId, authenticate, userId.(string), path, action)
			common.LogConfig.Infof(logMessage)
			pass := checkAction(xMerchantId, authenticate, userId.(string), path, action)
			common.LogConfig.Infof("[%v] :: Result checkAction :: %v", sessionRequest, pass)
			if pass {
				return
			}
		}
		panic(lang.GetMessageByKey(common.UnAuthorization, req))
	}
}

func decryptionPem(paramPem string, req *http.Request, prefix string) {
	sessionRequest := req.Header.Get("mobio-session-api-m")
	common.LogConfig.Infof("[%v] :: Start decryptionPem", sessionRequest)
	authType, token, singKey, authenticate := getItemTokenByHeader("", req)
	if authType == common.TypicallyBasic {
		return
	}
	xMerchantId := req.Header.Get("X-Merchant-Id")
	userId := getValueByKeyInToken(token, "id")
	paramPem = unquotePemParam(paramPem)
	numberItem := len(paramPem) % 4
	newParamPem := paramPem
	for i := 0; i < numberItem; i++ {
		newParamPem += "="
	}

	// Check power pem
	isCheckPowerPem := false
	if env.GetApplicationConfig().PowerPem != "" {
		powerPem := env.GetApplicationConfig().PowerPem
		powerPem = unquotePemParam(powerPem)
		numberItemPowerPem := len(powerPem) % 4
		for i := 0; i < numberItemPowerPem; i++ {
			powerPem += "="
		}
		if powerPem == newParamPem {
			isCheckPowerPem = true
		}
	}
	// Giải mã theo dạng mới
	ikey := md5.Sum([]byte(common.AesCipherKey + singKey))
	iiv := md5.Sum([]byte(common.AesCipherIv + singKey))

	parts := handleDecryptionPem(newParamPem, ikey, iiv)
	isCheckPermission := parts[0]
	action := parts[1]
	checkSum := parts[2]
	path := parts[3]
	if isCheckPowerPem {
		common.LogConfig.Debugf("[%v] :: Api use powerpem", sessionRequest)
		return
	}
	if v, _ := strconv.Atoi(isCheckPermission); v > 0 {
		pathUri := req.URL.Path
		newProtocol := req.Header.Get("x-forwarded-proto")
		if newProtocol == "" {
			newProtocol = "https"
		}
		newHost := req.Host

		common.LogConfig.Infof("newHost :: %v", newHost)
		if utils.IsValueEmpty(newHost) {
			newHost = "test1.mobio.vn"
		}

		newPort := req.Header.Get("x-port")
		newSever := fmt.Sprintf("%v://%v", newProtocol, newHost)
		if newPort != "" {
			newSever += fmt.Sprintf(":%v", newPort)
		}
		if prefix != common.PrefixPathWeb {
			newSever += fmt.Sprintf("%v", prefix)
		}
		newSever += "/"
		newBaseUrl, _ := url.JoinPath(newSever, pathUri)
		common.LogConfig.Infof("[%v] :: newBaseUrl :: %v", sessionRequest, newBaseUrl)
		serverSignature := md5.Sum([]byte(newBaseUrl))
		sumServerSignature := hex.EncodeToString(serverSignature[:])
		common.LogConfig.Infof("[%v] :: sumServerSignature :: %v", sessionRequest, sumServerSignature)
		common.LogConfig.Infof("[%v] :: checkSum :: %v", sessionRequest, checkSum)
		if checkSum == sumServerSignature {
			logMessage := fmt.Sprintf("[%v] :: Start checkAction-xMerchantId :: %v, authenticate :: %v, userID :: %v, path :: %v, action :: %v ",
				sessionRequest, xMerchantId, authenticate, userId.(string), path, action)
			common.LogConfig.Infof(logMessage)
			pass := checkAction(xMerchantId, authenticate, userId.(string), path, action)
			common.LogConfig.Infof("[%v] :: Result checkAction :: %v", sessionRequest, pass)
			if pass {
				return
			}
		}
		panic(lang.GetMessageByKey(common.UnAuthorization, req))
	}

}

func versionCheckPemFromClient(req *http.Request) string {
	versionClient := req.Header.Get("Client-Version")
	switch versionClient {
	case "v4.45.0":
		return common.VersionTwo
	default:
		return common.VersionOne
	}
}

func verifyTokenApiGuest(req *http.Request, isVerifyTokenExpired bool) {
	inputHeaders := req.Header
	authenticateData := inputHeaders.Get("Authorization")
	merchantId := inputHeaders.Get("X-Merchant-Id")
	if merchantId == "" {
		panic("Required X-Merchant-Id header")
	}
	if authenticateData == "" {
		panic("Required Token Header")
	}
	splitAuthenticateData := strings.Split(authenticateData, " ")
	authType, tokenData := splitAuthenticateData[0], splitAuthenticateData[1]
	if authType != common.TypicallyBearer {
		panic("Token type not supported")
	}
	sysConfigJwt := call_module_other.GetInfoConfigJwtAnonymous(merchantId)
	algorithm := sysConfigJwt["algorithm"].(string)
	secretKey := sysConfigJwt["secret_key"].(string)
	// Giải mã token
	mapToken, err := genTokenAnonymous(tokenData, algorithm, secretKey, isVerifyTokenExpired, req)
	if err != nil {
		panic(err.Error())
	}
	jwtType := mapToken["jwt_type"]
	if jwtType != "anonymous" {
		panic("Token type not supported")
	}
	merchantIdToken := mapToken["merchant_id"].(string)
	if merchantId != merchantIdToken {
		panic("Merchant id error")
	}
}

func genTokenAnonymous(tokenString string, algorithm string, secretKey string, isVerifyTokenExpired bool, req *http.Request) (map[string]interface{}, error) {
	common.LogConfig.Debugf("[%v] :: tokenString :: ", tokenString)
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})

	if !token.Valid {
		ve, ok := err.(*jwt.ValidationError)
		if ok && ve.Errors&jwt.ValidationErrorMalformed != 0 {
			panic(lang.GetMessageByKey(common.UnAuthentice, req))
		} else if ok && ve.Errors&(jwt.ValidationErrorExpired|jwt.ValidationErrorNotValidYet) != 0 {
			if isVerifyTokenExpired {
				// Token is either expired or not active yet
				panic(lang.GetMessageByKey(common.UnAuthentice, req))
			}
		} else {
			return nil, fmt.Errorf("couldn't handle this token: %v", err)
		}
	}
	// Kiểm tra nếu claims không nil và token hợp lệ
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("error extracting claims")
	}

	// Chuyển đổi claims thành map[string]interface{}
	resultToken := make(map[string]interface{})
	for key, val := range claims {
		resultToken[key] = val
	}
	// Trả về lỗi nếu token không hợp lệ hoặc không phù hợp với secret key
	return resultToken, nil
}

func saveCacheJWTCallAPI(merchantId string, accountId string, jwtId string, deviceType string) {
	tsNow := utils.GetTimeStampNow()
	keyJWTMerchant := "admin_jwt_active_call_api_for_merchant#" + merchantId
	jwtAccountInfo := map[string]interface{}{
		"account_id":  accountId,
		"active_time": tsNow,
		"device_type": deviceType,
	}
	jsonData, _ := json.Marshal(jwtAccountInfo)
	valueStr := string(jsonData)
	caching.SaveCacheHSet(keyJWTMerchant, jwtId, valueStr)

}
