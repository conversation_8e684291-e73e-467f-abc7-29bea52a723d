package handlers

import (
	"api-m/common"
	"api-m/config/lang"
	"net/http"
)

func PublicPu(w http.ResponseWriter, r *http.Request) {
	sessionRequest := r.Header.Get("mobio-session-api-m")
	pathApi := r.URL.Path
	common.LogConfig.Infof("[%v] :: Start PublicPu Path API :: %v", sessionRequest, pathApi)
	method := r.Method
	merchantIdHeader := r.Header.Get("X-Merchant-Id")
	resultCheckBlackList := checkApiBlackListByPath(merchantIdHeader, pathApi, method)
	if resultCheckBlackList {
		panic("Bạn không có quyền truy cập chức năng này!")
	}

	checkSpecialApiIfHas(r)
	resultCheckPath := checkApiAcceptedByPath(pathApi, common.PathApiExclude())
	// resultCheckPathRedirect := checkApiAcceptedByPath(pathApi, common.PathApiRedirect())
	common.LogConfig.Infof("[%v] :: Start resultCheckPath Path API :: %v", sessionRequest, resultCheckPath)
	if !checkApiAcceptedByPath(pathApi, common.PathApiPublicPuAcceptedApis()) && !resultCheckPath {
		common.LogConfig.Infof("[%v] :: Result check path undeclared variable :: %v", sessionRequest, pathApi)
		panic(lang.GetMessageByKey(common.UnAuthorization, r))
	}


	if !resultCheckPath && !autoPassPemByMerchantId(merchantIdHeader) {
		authType := VerifyMerchant(r)
		pathSpecial, responseFix := checkFixResponseApiIfHas(pathApi)
		if pathSpecial {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			_, err := w.Write([]byte(responseFix))
			if err != nil {
				panic(err)
			}
		}
		if authType != common.TypicallyBasic {
			CheckPermission(r, common.PrefixPathPbuser)
		}
	}

	processForwardApi(w, r, common.TypeForwardPublicPu, nil)
}
