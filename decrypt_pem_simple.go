package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net/url"
	"strings"
)

// decryptPemSimple - simplified approach to match JavaScript exactly
func decryptPemSimple(encryptedData, u string) (string, error) {
	// Step 1: URL decode
	decodedData, err := url.QueryUnescape(encryptedData)
	if err != nil {
		return "", fmt.Errorf("failed to URL decode: %v", err)
	}

	// Step 2: JWT and key generation (exactly like JavaScript)
	j := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5S87kehv0W3NMQRh4CIG5s3yjsQ0tUpXw5NLLSc7zs0"

	parts := strings.Split(j, ".")
	lastPart := parts[len(parts)-1]

	md5Hash := md5.Sum([]byte(lastPart + u))
	k := hex.EncodeToString(md5Hash[:])[:24]

	fmt.Printf("Key k: %s\n", k)

	// Step 3: Try different key/IV approaches
	// Approach 1: Use the 24-char hex as password for key derivation
	keyBytes, _ := hex.DecodeString(k)

	// Try CryptoJS-style key derivation
	// CryptoJS might use PBKDF1 or similar for key expansion
	fmt.Printf("Trying CryptoJS-style key derivation...\n")

	// Method 1: Use the hex string as salt for PBKDF1-like derivation
	salt := []byte(k)      // Use the hex string as salt
	password := []byte("") // Empty password

	// Simple PBKDF1-like iteration
	derived := md5.Sum(append(password, salt...))
	key1 := derived[:]

	// Second iteration for IV
	derived2 := md5.Sum(append(derived[:], append(password, salt...)...))
	iv1 := derived2[:]

	fmt.Printf("PBKDF1-style key: %x\n", key1)
	fmt.Printf("PBKDF1-style IV:  %x\n", iv1)

	result := tryDecrypt(decodedData, key1, iv1)
	if strings.Contains(result, "login:") {
		fmt.Printf("SUCCESS with PBKDF1-style!\n")
		return result, nil
	}

	// Try multiple approaches with different IV strategies
	keyApproaches := []string{"zero_pad", "repeat_pattern", "truncate_md5", "pbkdf1_style"}
	ivApproaches := []string{"same_as_key", "zero_iv", "first_12_bytes"}

	for _, keyApproach := range keyApproaches {
		for _, ivApproach := range ivApproaches {
			fmt.Printf("\nTrying key: %s, IV: %s\n", keyApproach, ivApproach)

			var finalKey, finalIV []byte

			// Generate key
			switch keyApproach {
			case "zero_pad":
				finalKey = make([]byte, 16)
				copy(finalKey, keyBytes)

			case "repeat_pattern":
				finalKey = make([]byte, 16)
				for i := 0; i < 16; i++ {
					finalKey[i] = keyBytes[i%len(keyBytes)]
				}

			case "truncate_md5":
				fullHash := hex.EncodeToString(md5Hash[:])
				finalKey, _ = hex.DecodeString(fullHash)

			case "pbkdf1_style":
				finalKey = key1
			}

			// Generate IV
			switch ivApproach {
			case "same_as_key":
				finalIV = make([]byte, len(finalKey))
				copy(finalIV, finalKey)

			case "zero_iv":
				finalIV = make([]byte, 16)

			case "first_12_bytes":
				finalIV = make([]byte, 16)
				copy(finalIV, keyBytes)
			}

			fmt.Printf("Key: %x\n", finalKey)
			fmt.Printf("IV:  %x\n", finalIV)

			// Try decryption
			result := tryDecrypt(decodedData, finalKey, finalIV)
			if len(result) > 50 {
				fmt.Printf("Result: %s...\n", result[:50])
			} else {
				fmt.Printf("Result: %s\n", result)
			}

			// Check if it looks like the expected result
			if strings.Contains(result, "login:") {
				fmt.Printf("SUCCESS! Found working approach: key=%s, iv=%s\n", keyApproach, ivApproach)
				return result, nil
			}
		}
	}

	return "", fmt.Errorf("no working approach found")
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func tryDecrypt(data string, key, iv []byte) string {
	defer func() {
		if r := recover(); r != nil {
			// Ignore panics and return empty string
		}
	}()

	ciphertext, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return ""
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return ""
	}

	if len(iv) != aes.BlockSize {
		return ""
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphertext, ciphertext)

	// Try to remove PKCS7 padding
	if len(ciphertext) == 0 {
		return ""
	}

	paddingLength := int(ciphertext[len(ciphertext)-1])
	if paddingLength > 0 && paddingLength <= 16 && paddingLength <= len(ciphertext) {
		// Validate padding
		valid := true
		for i := len(ciphertext) - paddingLength; i < len(ciphertext); i++ {
			if ciphertext[i] != byte(paddingLength) {
				valid = false
				break
			}
		}
		if valid {
			ciphertext = ciphertext[:len(ciphertext)-paddingLength]
		}
	}

	return string(ciphertext)
}

func main() {
	encryptedData := "cRU%2BNwmZEdOc9TlP9BEPMB47iZOhapu6WiKbQZZShEeYEnLDz%2Bu5DdQEOYEfgqcTSjHXhJvYydgd623RsMhf6QwXBlLkRyBzI2w6Tu0cTuEy%2BkHGGgPnXgPV9IaMo8sCCD3tK072QwzdDgxiVcAYl5QqjN1YrsvhiUaW%2F3MfB8S8aMwtYcV5VuHpxV%2BWPbMx"
	u := "https://release.mobio.vn/adm/api/v2.1/merchants/57d559c1-39a1-4cee-b024-b953428b5ac8/public-configs"

	result, err := decryptPemSimple(encryptedData, u)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	fmt.Printf("\nFinal result: %s\n", result)

	expected := "login:vZ/OLU@.$io?SmB>uU*bQ.g{MWmhS^UF:87737cb2faf2045f5c068928ae4aedb8:<ig>CZ%K,0*S5$T1QfwUn/umjb,%[i:4dn&TPy%@6(-izrh0NlUV*AWv3k>7lSE"
	fmt.Printf("Expected: %s\n", expected)
	fmt.Printf("Match: %t\n", result == expected)
}
