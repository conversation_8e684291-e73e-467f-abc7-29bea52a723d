// Test script to understand CryptoES behavior
const CryptoES = require('crypto-es').default;
const md5 = require('md5');

const j = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5S87kehv0W3NMQRh4CIG5s3yjsQ0tUpXw5NLLSc7zs0";
const u = "https://release.mobio.vn/adm/api/v2.1/merchants/57d559c1-39a1-4cee-b024-b953428b5ac8/public-configs";

const lastPart = j.split('.').pop() || '';
console.log('Last part:', lastPart);

const concatenated = lastPart + u;
console.log('Concatenated:', concatenated);

const k = md5(concatenated).substring(0, 24);
console.log('k (24 chars):', k);

const key = CryptoES.enc.Hex.parse(k);
const iv = CryptoES.enc.Hex.parse(k);

console.log('Key object:', key);
console.log('Key words:', key.words);
console.log('Key sigBytes:', key.sigBytes);
console.log('IV object:', iv);
console.log('IV words:', iv.words);
console.log('IV sigBytes:', iv.sigBytes);

// Convert to actual bytes for comparison
const keyBytes = [];
const ivBytes = [];

for (let i = 0; i < key.words.length; i++) {
    const word = key.words[i];
    keyBytes.push((word >>> 24) & 0xff);
    keyBytes.push((word >>> 16) & 0xff);
    keyBytes.push((word >>> 8) & 0xff);
    keyBytes.push(word & 0xff);
}

for (let i = 0; i < iv.words.length; i++) {
    const word = iv.words[i];
    ivBytes.push((word >>> 24) & 0xff);
    ivBytes.push((word >>> 16) & 0xff);
    ivBytes.push((word >>> 8) & 0xff);
    ivBytes.push(word & 0xff);
}

console.log('Key bytes:', keyBytes.slice(0, key.sigBytes));
console.log('IV bytes:', ivBytes.slice(0, iv.sigBytes));
console.log('Key hex:', keyBytes.slice(0, key.sigBytes).map(b => b.toString(16).padStart(2, '0')).join(''));
console.log('IV hex:', ivBytes.slice(0, iv.sigBytes).map(b => b.toString(16).padStart(2, '0')).join(''));

// Test the actual decryption
const encryptedData = 'cRU+NwmZEdOc9TlP9BEPMB47iZOhapu6WiKbQZZShEeYEnLDz+u5DdQEOYEfgqcTSjHXhJvYydgd623RsMhf6QwXBlLkRyBzI2w6Tu0cTuEy+kHGGgPnXgPV9IaMo8sCCD3tK072QwzdDgxiVcAYl5QqjN1YrsvhiUaW/3MfB8S8aMwtYcV5VuHpxV+WPbMx';

const mode = CryptoES.mode.CBC;
const padding = CryptoES.pad.Pkcs7;
const options = { iv: iv, mode: mode, padding: padding };

console.log('Decryption options:', options);

try {
    const decrypted = CryptoES.AES.decrypt(encryptedData, key, options);
    const decryptedText = decrypted.toString(CryptoES.enc.Utf8);
    console.log('Decrypted text:', decryptedText);

    // Let's also see what happens if we manually expand the key to 16 bytes
    const expandedKeyBytes = new Array(16);
    for (let i = 0; i < 16; i++) {
        expandedKeyBytes[i] = keyBytes[i % keyBytes.length];
    }
    console.log('Expanded key bytes (repeating):', expandedKeyBytes);
    console.log('Expanded key hex (repeating):', expandedKeyBytes.map(b => b.toString(16).padStart(2, '0')).join(''));

    // Zero-padded version
    const zeroPaddedKeyBytes = [...keyBytes];
    while (zeroPaddedKeyBytes.length < 16) {
        zeroPaddedKeyBytes.push(0);
    }
    console.log('Zero-padded key bytes:', zeroPaddedKeyBytes);
    console.log('Zero-padded key hex:', zeroPaddedKeyBytes.map(b => b.toString(16).padStart(2, '0')).join(''));

    // Let's try to understand how CryptoJS internally handles the key
    // Create a manual AES cipher to see what key it actually uses
    const cipher = CryptoES.algo.AES.createDecryptor(key, options);
    console.log('Cipher key:', cipher._key);
    console.log('Cipher key words:', cipher._key.words);
    console.log('Cipher key sigBytes:', cipher._key.sigBytes);

    // Convert cipher key to bytes
    const cipherKeyBytes = [];
    for (let i = 0; i < cipher._key.words.length; i++) {
        const word = cipher._key.words[i];
        cipherKeyBytes.push((word >>> 24) & 0xff);
        cipherKeyBytes.push((word >>> 16) & 0xff);
        cipherKeyBytes.push((word >>> 8) & 0xff);
        cipherKeyBytes.push(word & 0xff);
    }
    console.log('Cipher key bytes:', cipherKeyBytes.slice(0, cipher._key.sigBytes));
    console.log('Cipher key hex:', cipherKeyBytes.slice(0, cipher._key.sigBytes).map(b => b.toString(16).padStart(2, '0')).join(''));

} catch (error) {
    console.error('Decryption error:', error);
}
