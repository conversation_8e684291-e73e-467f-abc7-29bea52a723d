package file

import (
	"bufio"
	"fmt"
	"mime"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func buildFilename(fileName string, contentType string) (string, error) {
	extensionFilename := filepath.Ext(fileName)
	fileNameNotExt := strings.TrimSuffix(fileName, extensionFilename)
	// extensions, err := mime.ExtensionsByType(contentType)
	// if err != nil {
	// 	return fileName, err
	// }
	contentTypeByExt := mime.TypeByExtension(extensionFilename)
	if contentType != contentTypeByExt {
		extension, err := mime.ExtensionsByType(contentType)
		if err == nil {
			return fmt.Sprintf("%s%s", fileNameNotExt, extension[0]), nil
		}
		return fmt.Sprintf("%s%s", fileNameNotExt, extensionFilename), nil
	}
	return fileName, nil

}

func contains(arr []string, str string) bool {
	for _, v := range arr {
		if v == str {
			return true
		}
	}
	return false
}

func TestValidateFile(t *testing.T) {
	fileName := "Clipboard Oct 10.png"
	file, err := os.Open("/Users/<USER>/Downloads/Clipboard Oct 10.png")
	extension := filepath.Ext(fileName)
	// fileNameNotExt := filepath.Ext(fileName)

	// Lấy tên file (không bao gồm phần mở rộng)
	fileNameNotExt := strings.TrimSuffix(fileName, extension)
	if err != nil {
		fmt.Println(err)
	}
	defer file.Close()

	stats, statsErr := file.Stat()
	if statsErr != nil {
		fmt.Println(statsErr)
	}

	var size int64 = stats.Size()
	bytes := make([]byte, size)

	bufr := bufio.NewReader(file)
	bufr.Read(bytes)
	isValid, contentType := ValidateExtensionFileUploadInAllowType(bytes)
	// kind, _ := filetype.Match(bytes)
	fmt.Println(isValid, contentType)
	cT := mime.TypeByExtension(extension)
	fmt.Println(fileNameNotExt)
	fmt.Println(cT)
	fmt.Println(buildFilename(fileName, contentType))

}
