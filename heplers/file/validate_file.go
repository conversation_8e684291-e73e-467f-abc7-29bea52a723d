package file

import (
	"api-m/common"
	"mime"
	"net/http"

	"github.com/h2non/filetype"
)

var (
	ExtensionImageAllow    = []string{"cgm", "g3", "jp2", "ktx", "pict", "pic", "pct", "btif", "sgi", "svgz", "psd", "uvi", "uvvi", "uvg", "uvvg", "djvu", "djv", "dwg", "dxf", "fbs", "fpx", "fst", "mmr", "rlc", "mdi", "wdp", "npx", "wbmp", "xif", "webp", "3ds", "cmx", "fh", "fhc", "fh4", "fh5", "fh7", "pntg", "pnt", "mac", "sid", "pcx", "qtif", "qti", "tga", "bmp", "gif", "ief", "jpg", "jpe", "jpeg", "png", "tiff", "tif", "ico", "ras", "pnm", "pbm", "pgm", "ppm", "rgb", "xbm", "xpm", "xwd"}
	ExtensionDocumentAllow = []string{"doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf"}
	ExtensionAudioAllow    = []string{"mp3", "mp4", "mov", "avi", "3gp", "3g2", "m4v", "mpeg", "mpg", "ogv", "webm", "flv", "mkv", "asx", "wmv", "hevc", "h.264"}
	ExtensionZipAllow      = []string{"zip", "rar"}
	ExtensionFontAllow     = []string{"ttf", "otf", "woff", "woff2"}
	ExtensionCustomAllow   = []string{"mopage"}

	// Custom riêng file của mobo
	mopageType = filetype.NewType("mopage", "mopage/mopage")
	pptType = filetype.NewType("ppt", "application/vnd.ms-powerpoint")
)

func mopageMatcher(buf []byte) bool {
	newBytes := []byte{
		0x4d, 0x4f, 0x42, 0x49, 0x4f, 0x20, 0x57, 0x45, 0x42, 0x20,
		0x45, 0x44, 0x49, 0x54, 0x4f, 0x52, 0x20, 0xc2, 0xa9, 0x20,
		0x32, 0x30, 0x32, 0x33,
	}

	if len(buf) < len(newBytes) {
		return false
	}

	for i := 0; i < len(newBytes); i++ {
		if buf[i] != newBytes[i] {
			return false
		}
	}

	return true
}

func pptMatcher(buf []byte) bool {
	newBytes := []byte{
		0xD0, 0xCF, 0x11, 0xE0,
	}

	if len(buf) < len(newBytes) {
		return false
	}

	for i := 0; i < len(newBytes); i++ {
		if buf[i] != newBytes[i] {
			return false
		}
	}

	return true
}

func concatMultipleSlices[T any](slices [][]T) []T {
	var totalLen int

	for _, s := range slices {
		totalLen += len(s)
	}

	result := make([]T, totalLen)

	var i int

	for _, s := range slices {
		i += copy(result[i:], s)
	}

	return result
}

func getExtensionFileByBuf(buf []byte) (bool, string) {

	filetype.AddMatcher(mopageType, mopageMatcher)
	filetype.AddMatcher(pptType, pptMatcher)

	kind, _ := filetype.Match(buf)
	if kind == filetype.Unknown {
		return false, "Unknown"
	}
	return true, kind.Extension
}

func ValidateExtensionFileUploadInAllowType(buf []byte) (bool, string) {
	AllExtensionAllow := concatMultipleSlices([][]string{ExtensionAudioAllow, ExtensionImageAllow, ExtensionDocumentAllow, ExtensionZipAllow, ExtensionFontAllow, ExtensionCustomAllow})
	status, extension := getExtensionFileByBuf(buf)
	common.LogConfig.Infof("ValidateExtensionFileUploadInAllowType :: extension :: %v", extension)
	common.IntCustomMimeType()
	contentType := mime.TypeByExtension("." + extension)
	if status {
		for _, value := range AllExtensionAllow {
			if value == extension {
				return true, contentType
			}
		}
	}
	return false, http.DetectContentType(buf)
}
