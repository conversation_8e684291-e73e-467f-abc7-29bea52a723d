package ase_cipher

import (
	"api-m/common"
	"fmt"
	"testing"
)

func TestDecrypt(t *testing.T) {
	key, iv := GetKeyIv(common.AesCipherKey, common.AesCipherIv)
	fmt.Println(key, iv)

	pemEnc := "hK8O7uhiw0d8NqgnqCC6Of29Vd028l4K/ABSsuhXWD7VN0VlFasqvyBzbolfPsI8Ed16v7HUfAMLBkwv0wseych/YQ7aVtQ2xQZNo1loo2k="
	pemTxt := Decrypt(pemEnc, []byte(key), []byte(iv))
	fmt.Println(pemTxt)
}


func TestEncryt(t *testing.T) {
	key, iv := GetKeyIv(common.AesCipherKey, common.AesCipherIv)
	fmt.Println(key, iv)

	pemTxt := "1:1:1:1:1"
	pemEnc := Encrypt(pemTxt, []byte(key), []byte(iv))
	fmt.Println(pemEnc)
}