package ase_cipher

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/hex"
)

func GetKeyIv(key, iv string) (string, string) {
	ikey, _ := hex.DecodeString(key)
	iiv, _ := hex.DecodeString(iv)
	return string(ikey), string(iiv)
}

func Decrypt(EData string, ikey []byte, iiv []byte) string {
	ciphertext, _ := base64.StdEncoding.DecodeString(EData)

	block, _ := aes.NewCipher(ikey)
	mode := cipher.NewCBCDecrypter(block, iiv[:aes.BlockSize])
	mode.CryptBlocks(ciphertext, ciphertext)
	return string(ciphertext[:len(ciphertext)-int(ciphertext[len(ciphertext)-1])])
}
