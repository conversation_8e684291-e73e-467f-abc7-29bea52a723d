package logger

import (
	"fmt"
	"os"
	"runtime"
	"strings"

	"github.com/sirupsen/logrus"
)

var Log *Logger

type Logger struct {
	*logrus.Logger
}

func formatFilePath(path string) string {
	arr := strings.Split(path, "/")
	return arr[len(arr)-1]
}
func ConfigLogger() *Logger {
	formatter := &logrus.TextFormatter{
		TimestampFormat:        "02-01-2023 15:04:05",
		FullTimestamp:          true,
		DisableLevelTruncation: true, // log level field configuration
		CallerPrettyfier: func(f *runtime.Frame) (string, string) {
			return "", fmt.Sprintf("[%s] [%s - line :: %d] [LogMessage]", formatFilePath(f.File), f.Function, f.Line)
		},
	}

	var baseLogger = logrus.New()

	var standardLogger = &Logger{baseLogger}

	standardLogger.Formatter = formatter
	standardLogger.SetReportCaller(true)
	standardLogger.SetOutput(os.Stdout)
	standardLogger.SetLevel(logrus.DebugLevel)

	return standardLogger
}

func StartLogger() *Logger {
	Log = ConfigLogger()
	return Log
}
