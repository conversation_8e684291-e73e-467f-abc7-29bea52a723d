package call_module_other

import (
	"api-m/config/env"
	"api-m/config/url_api_other"
	"api-m/heplers/caching"
	"api-m/heplers/utils"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

func GetDetailMerchantConfig(merchantId string) any {
	if utils.IsValueEmpty(merchantId) {
		merchantId = "mobio"
	}
	// Get value from redis
	keyCache := caching.BuildKeyCacheByInput("GetMerchantConfigHost", merchantId)
	valueCache := caching.GetValueByKey(keyCache, true, true)
	if !utils.IsValueEmpty(valueCache) {
		return valueCache.(map[string]interface{})
	}
	adminUrl := fmt.Sprintf(url_api_other.DetailMerchantConfig, env.GetApplicationConfig().AdminHost, merchantId)
	req, err := http.NewRequest(http.MethodGet, adminUrl, nil)
	if err != nil {
		return nil
	}
	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil
	}
	if res.StatusCode == http.StatusOK {
		var result map[string]any
		err := json.NewDecoder(res.Body).Decode(&result)
		if err != nil {
			return nil
		}
		resultData := result["data"]
		caching.SetKeyByValue(keyCache, resultData, 24*time.Hour, true)
		return resultData
	}
	return nil
}

func GetMerchantCodeByMerchantId(merchantId string) string {
	if utils.IsValueEmpty(merchantId) {
		merchantId = "mobio"
		return "MOBIO"
	}
	keyCache := caching.BuildKeyCacheByInput("GetMerchantCodeByMerchantId", merchantId)
	valueCache := caching.GetValueByKey(keyCache, true, true)
	if !utils.IsValueEmpty(valueCache) {
		return valueCache.(string)
	}
	adminUrl := fmt.Sprintf(url_api_other.DetailByMerchantId, env.GetApplicationConfig().AdminHost, merchantId)
	req, err := http.NewRequest(http.MethodGet, adminUrl, nil)
	if err != nil {
		return ""
	}
	req.Header.Add("X-Merchant-ID", merchantId)
	req.Header.Add("Authorization", fmt.Sprintf("Basic %v", env.GetApplicationConfig().YEK_REWOP))
	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return ""
	}
	if res.StatusCode == http.StatusOK {
		var result map[string]any
		err := json.NewDecoder(res.Body).Decode(&result)
		if err != nil {
			return ""
		}
		resultData := result["data"].(map[string]any)
		merchantCode := resultData["code"].(string)
		caching.SetKeyByValue(keyCache, merchantCode, 24*time.Hour, true)
		return merchantCode
	}
	return ""
}

func GetInfoConfigJwtAnonymous(merchantId string) map[string]interface{} {
	if utils.IsValueEmpty(merchantId) {
		merchantId = "mobio"
	}
	// Get value from redis
	keyCache := caching.BuildKeyCacheByInput("GetInfoConfigJwtAnonymous", merchantId)
	valueCache := caching.GetValueByKey(keyCache, true, true)
	if !utils.IsValueEmpty(valueCache) {
		return valueCache.(map[string]interface{})
	}
	adminUrl := fmt.Sprintf(url_api_other.GetInfoConfigJwtAnonymous, env.GetApplicationConfig().AdminHost)
	req, err := http.NewRequest(http.MethodGet, adminUrl, nil)
	if err != nil {
		return nil
	}
	req.Header.Add("X-Merchant-ID", merchantId)
	req.Header.Add("Authorization", fmt.Sprintf("Basic %v", env.GetApplicationConfig().YEK_REWOP))
	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil
	}
	if res.StatusCode == http.StatusOK {
		var result map[string]interface{}
		err := json.NewDecoder(res.Body).Decode(&result)
		if err != nil {
			return nil
		}
		value := result["data"].(map[string]interface{})
		caching.SetKeyByValue(keyCache, value, 24*time.Hour, true)
		return value
	}
	return nil
}

func GetMerchantConfigHost(merchantId string, keyHost string) any {
	detailMerchantConfig := GetDetailMerchantConfig(merchantId)
	convert := detailMerchantConfig.(map[string]interface{})
	result, _ := convert[keyHost]
	if result == nil {
		return ""
	}
	return result.(string)
}
