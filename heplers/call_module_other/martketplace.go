package call_module_other

import (
	"api-m/config/env"
	"api-m/config/url_api_other"
	"encoding/json"
	"fmt"
	"net/http"
)

func GetInfoWebPushByTrackingCode(merchantId string, trackingCode string) map[string]interface{} {
	url := fmt.Sprintf(url_api_other.GetInfoWebPushByTrackingCode, "http://market-place-app-api-service.mobio/", trackingCode)
	// url := fmt.Sprintf(url_api_other.GetInfoWebPushByTrackingCode, "http://127.0.0.1:5009/", trackingCode)
	
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil
	}
	req.Header.Add("X-Merchant-ID", merchantId)
	req.Header.Add("Authorization", fmt.Sprintf("Basic %v", env.GetApplicationConfig().YEK_REWOP))
	res, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil
	}
	if res.StatusCode == http.StatusOK {
		var result map[string]interface{}
		err := json.NewDecoder(res.Body).Decode(&result)
		if err != nil {
			return nil
		}
		return result["data"].(map[string]interface{})
	}
	return nil
}