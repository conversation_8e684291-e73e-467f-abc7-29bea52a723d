package caching

import (
	"api-m/common"
	"api-m/config/env"
	"context"
	"encoding/json"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
)

type ConnRedis struct {
	conn *redis.Client
}

type ConnClusterRedis struct {
	conn *redis.ClusterClient
}

var lock = &sync.Mutex{}

var singleConnRedis interface{}
var singleConnAdminRedis interface{}

func setupConnectRedisNormal() interface{} {
	redisUri := env.ConfigEnvRedis().RedisUri
	redisBaseUri := env.ConfigEnvRedis().RedisBaseUri
	redisBaseType := env.ConfigEnvRedis().RedisBaseType

	// Kiểm tra và tạo kết nối Redis nếu cần
	if singleConnRedis == nil {
		lock.Lock()
		defer lock.Unlock()
		if singleConnRedis == nil {
			common.LogConfig.Infof("Creating single instance connect redis now.")

			// Tạo options Redis dựa trên loại kết nối
			if redisBaseType != common.RedisTypeCluster {
				singleConnRedis = connectRedisSingle(redisUri)
			} else {
				singleConnRedis = connectRedisCluster(redisBaseUri)
			}

		}
	}
	return singleConnRedis
}

func connectRedisSingle(redisSingeUri string) *redis.Client {
	opt, err := redis.ParseURL(redisSingeUri)
	if err != nil {
		common.LogConfig.Errorf("Cannot connect redis :: Error :: %v", err.Error())
		panic(err)
	}
	connNew := redis.NewClient(opt)
	return connNew
}

func connectRedisCluster(redisClusterUri string) *redis.ClusterClient {

	parsedUrl, err := url.Parse(redisClusterUri)
	if err != nil {
		common.LogConfig.Errorf("Cannot connect redis :: Error :: %v", err.Error())
		panic(err)
	}

	host := parsedUrl.Host

	addrs := []string{host}
	client := redis.NewClusterClient(&redis.ClusterOptions{
		Addrs: addrs,
	})
	return client
}

func setupConnectAdminRedis() interface{} {
	redisUri := env.ConfigEnvRedis().AdminRedisUri
	redisBaseType := env.ConfigEnvRedis().AdminRedisType
	if singleConnAdminRedis == nil {
		lock.Lock()
		defer lock.Unlock()
		if singleConnAdminRedis == nil {
			common.LogConfig.Infof("Creating single instance connect redis admin now.")
			if redisBaseType != common.RedisTypeCluster {
				singleConnAdminRedis = connectRedisSingle(redisUri)
			} else {
				singleConnAdminRedis = connectRedisCluster(redisUri)
			}
		}
	}
	return singleConnAdminRedis
}

func GetValueByKey(key string, isEnableGenKey bool, isCheckAdminRedis bool) interface{} {
	setupConn := setupConnectRedisNormal()
	if isCheckAdminRedis {
		setupConn = setupConnectAdminRedis()
	}

	newKey := key
	if isEnableGenKey {
		newKey = genKeyCacheByKeyInput(key)
	}
	t1 := time.Now()

	// Kiểm tra kiểu của setupConn và gọi phương thức Get tương ứng
	var value string
	var err error
	switch conn := setupConn.(type) {
	case *redis.Client:
		value, err = conn.Get(context.TODO(), newKey).Result()
	case *redis.ClusterClient:
		value, err = conn.Get(context.TODO(), newKey).Result()
	default:
		panic("Unexpected Redis client type")
	}

	common.LogConfig.Infof("Time process get key redis GetValueByKey :: %s :: %v", newKey, time.Since(t1))
	if err != nil {
		common.LogConfig.Errorf("GetValueByKey :: key :: %v :: err :: %v", key, err.Error())
	}
	return unmarshalValueToValueRedis(value)
}

func SetKeyByValue(key string, value interface{}, time time.Duration, isEnableGenKey bool) interface{} {
	setupConn := setupConnectAdminRedis()
	newKey := key
	if isEnableGenKey {
		newKey = genKeyCacheByKeyInput(key)
	}

	newValue := marshalValueToValueRedis(value)
	var err error
	switch conn := setupConn.(type) {
	case *redis.Client:
		err = conn.Set(context.TODO(), newKey, newValue, time).Err()
	case *redis.ClusterClient:
		err = conn.Set(context.TODO(), newKey, newValue, time).Err()
	default:
		panic("Unexpected Redis client type")
	}

	if err != nil {
		common.LogConfig.Errorf("SetKeyByValue :: key :: %v :: value :: %v ::err :: %v", key, value, err.Error())
	}
	return value
}

func marshalValueToValueRedis(value interface{}) []byte {
	valueNew, _ := json.Marshal(value)
	return valueNew
}

func unmarshalValueToValueRedis(value string) interface{} {
	common.LogConfig.Infof("Start unmarshalValueToValueRedis")
	var valueNew any
	json.Unmarshal([]byte(value), &valueNew)
	if valueNew == nil {
		return value
	}
	return valueNew
}

func genKeyCacheByKeyInput(key string) string {
	ArrayStr := []string{env.ConfigEnvRedis().RedisKeyCache}
	ArrayStr = append(ArrayStr, key)
	return strings.Join(ArrayStr, "#")
}

func BuildKeyCacheByInput(values ...string) string {
	return strings.Join(values, "#")
}

// Hàm lưu dữ liệu vào Redis bằng lệnh HSET
func SaveCacheHSet(key string, field string, value string) interface{} {
	setupConn := setupConnectAdminRedis()
	var err error
	switch conn := setupConn.(type) {
	case *redis.Client:
		err = conn.HSet(context.TODO(), key, field, value).Err()
	case *redis.ClusterClient:
		err = conn.HSet(context.TODO(), key, field, value).Err()
	default:
		panic("Unexpected Redis client type")
	}
	if err != nil {
		common.LogConfig.Errorf("SaveCacheHSet :: key :: %v :: field :: %v :: value :: %v ::err :: %v", key, field, value, err.Error())
	}
	return value
}

func SetExpireKey(key string, timeExpire time.Duration) {
	setupConn := setupConnectAdminRedis()
	var err error
	switch conn := setupConn.(type) {
	case *redis.Client:
		err = conn.Expire(context.TODO(), key, timeExpire).Err()
	case *redis.ClusterClient:
		err = conn.Expire(context.TODO(), key, timeExpire).Err()
	default:
		panic("Unexpected Redis client type")
	}
	if err != nil {
		common.LogConfig.Errorf("SetExpireKey :: key :: %v ::err :: %v", key, err.Error())
	}
}
