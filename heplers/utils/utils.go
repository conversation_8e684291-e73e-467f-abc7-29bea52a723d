package utils

import (
	"api-m/common"
	"api-m/config/lang"
	"api-m/heplers/respone"
	"github.com/google/uuid"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

func IsValueEmpty(value any) bool {
	if value == "" || value == 0 || value == nil {
		return true
	}
	return false
}

func genUUID() string {
	id := uuid.New()
	return id.String()
}

func BuildResponse() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			defer func() {
				if r := recover(); r != nil {
					switch e := r.(type) {
					case lang.StructureMessage:
						response := respone.Response{Code: e.Code, Message: e.Message, ResponseWriter: w}
						response.BuildResponseJson()
					case string:
						response := respone.Response{Code: http.StatusPreconditionFailed, Message: e, ResponseWriter: w}
						response.BuildResponseJson()
					default:
						response := respone.Response{Code: http.StatusInternalServerError, Message: "Có lỗi phát sinh trong server", ResponseWriter: w}
						response.BuildResponseJson()
					}
					return
				}
			}()
			next.ServeHTTP(w, r)
		})
	}
}

func AllowQuerySemicolons(h http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.RawQuery, ";") {
			r2 := new(http.Request)
			*r2 = *r
			r2.URL = new(url.URL)
			*r2.URL = *r.URL
			r2.URL.RawQuery = strings.ReplaceAll(r.URL.RawQuery, ";", common.MobioSpecialSemicolons)
			h.ServeHTTP(w, r2)
		} else {
			h.ServeHTTP(w, r)
		}
	})
}

func SetSessionHeader(h http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		session := genUUID()
		r.Header.Set("mobio-session-api-m", session)
		//common.LogConfig.Infof("Start process session :: %v", session)
		h.ServeHTTP(w, r)
	})
}

func LimitPerPage(h http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		per_page := r.URL.Query().Get("per_page")
		if !IsValueEmpty(per_page) {
			x, err := strconv.Atoi(per_page)
			if err == nil {
				if x > 1000 {
					panic(lang.GetMessageByKey(common.LimitPerPage, r))
				}
			}
		}
		h.ServeHTTP(w, r)
	})
}

func GetTimeStampNow() int64 {
	timestamp := time.Now().UTC().Unix()
	return timestamp
}
