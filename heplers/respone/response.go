package respone

import (
	"api-m/common"
	"encoding/json"
	"net/http"
)

type Response struct {
	ResponseWriter http.ResponseWriter
	Code           int
	Message        string
	Data           interface{}
}

func (reStruct *Response) BuildResponseJson() {
	reStruct.ResponseWriter.Header().Set("Content-Type", "application/json")
	reStruct.ResponseWriter.WriteHeader(reStruct.Code)
	data := map[string]interface{}{
		"code":    reStruct.Code,
		"message": reStruct.Message,
	}
	if reStruct.Data != nil {
		data["data"] = reStruct.Data
	}

	err := json.NewEncoder(reStruct.ResponseWriter).Encode(data)
	if err != nil {
		common.LogConfig.Errorf("BuildResponseJson :: err :: %v", err)
	}
}
