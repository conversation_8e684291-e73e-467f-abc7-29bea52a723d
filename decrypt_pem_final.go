package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net/url"
	"strings"
)

// decryptPem - Go equivalent of JavaScript decryptPem function
// Note: This implementation attempts to replicate the JavaScript behavior
// but CryptoJS has internal key handling that's difficult to replicate exactly
func decryptPem(encryptedData, u string) (string, error) {
	// Step 1: URL decode the encrypted data
	decodedData, err := url.QueryUnescape(encryptedData)
	if err != nil {
		return "", fmt.Errorf("failed to URL decode: %v", err)
	}
	fmt.Printf("Decoded data: %s\n", decodedData)

	// Step 2: JWT token (hardcoded as in the original JS)
	j := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5S87kehv0W3NMQRh4CIG5s3yjsQ0tUpXw5NLLSc7zs0"

	// Step 3: Extract the last part after the final dot
	parts := strings.Split(j, ".")
	var lastPart string
	if len(parts) > 0 {
		lastPart = parts[len(parts)-1]
	}
	fmt.Printf("Last part of JWT: %s\n", lastPart)

	// Step 4: Generate MD5 hash and take first 24 characters
	md5Hash := md5.Sum([]byte(lastPart + u))
	k := hex.EncodeToString(md5Hash[:])[:24]
	fmt.Printf("Key k (24 chars): %s\n", k)

	// Step 5: Parse the 24-character hex string (like CryptoES.enc.Hex.parse)
	keyBytes, err := hex.DecodeString(k)
	if err != nil {
		return "", fmt.Errorf("failed to decode key hex: %v", err)
	}
	fmt.Printf("Key bytes: %x (length: %d)\n", keyBytes, len(keyBytes))

	// Step 6: Use the same bytes for IV (as in the original JS code)
	ivBytes := make([]byte, len(keyBytes))
	copy(ivBytes, keyBytes)
	fmt.Printf("IV bytes: %x (length: %d)\n", ivBytes, len(ivBytes))

	// Step 7: Handle key and IV sizing for AES
	// This is where the challenge lies - CryptoJS handles 12-byte keys differently
	// than standard Go crypto libraries
	
	// Approach: Zero-pad to 16 bytes (most common approach)
	finalKey := make([]byte, 16)
	copy(finalKey, keyBytes)
	
	finalIV := make([]byte, 16)
	copy(finalIV, ivBytes)
	
	fmt.Printf("Final key: %x\n", finalKey)
	fmt.Printf("Final IV: %x\n", finalIV)

	// Step 8: Decrypt using AES-CBC
	ciphertext, err := base64.StdEncoding.DecodeString(decodedData)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %v", err)
	}

	block, err := aes.NewCipher(finalKey)
	if err != nil {
		return "", fmt.Errorf("failed to create AES cipher: %v", err)
	}

	mode := cipher.NewCBCDecrypter(block, finalIV)
	mode.CryptBlocks(ciphertext, ciphertext)

	// Step 9: Remove PKCS7 padding
	result := removePKCS7Padding(ciphertext)
	
	return result, nil
}

// removePKCS7Padding removes PKCS7 padding from decrypted data
func removePKCS7Padding(data []byte) string {
	if len(data) == 0 {
		return ""
	}
	
	paddingLength := int(data[len(data)-1])
	
	// Validate padding
	if paddingLength > len(data) || paddingLength == 0 || paddingLength > 16 {
		// Invalid padding, return as string anyway
		return string(data)
	}
	
	// Check if all padding bytes are the same
	for i := len(data) - paddingLength; i < len(data); i++ {
		if data[i] != byte(paddingLength) {
			// Invalid padding, return as string anyway
			return string(data)
		}
	}
	
	return string(data[:len(data)-paddingLength])
}

func main() {
	fmt.Println("=== Go AES-CBC Decryption (JavaScript CryptoES equivalent) ===")
	fmt.Println()
	
	// Test case from the JavaScript example
	encryptedData := "cRU%2BNwmZEdOc9TlP9BEPMB47iZOhapu6WiKbQZZShEeYEnLDz%2Bu5DdQEOYEfgqcTSjHXhJvYydgd623RsMhf6QwXBlLkRyBzI2w6Tu0cTuEy%2BkHGGgPnXgPV9IaMo8sCCD3tK072QwzdDgxiVcAYl5QqjN1YrsvhiUaW%2F3MfB8S8aMwtYcV5VuHpxV%2BWPbMx"
	u := "https://release.mobio.vn/adm/api/v2.1/merchants/57d559c1-39a1-4cee-b024-b953428b5ac8/public-configs"
	
	result, err := decryptPem(encryptedData, u)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	fmt.Printf("\nDecrypted result: %s\n", result)
	
	// Expected output for comparison
	expected := "login:vZ/OLU@.$io?SmB>uU*bQ.g{MWmhS^UF:87737cb2faf2045f5c068928ae4aedb8:<ig>CZ%K,0*S5$T1QfwUn/umjb,%[i:4dn&TPy%@6(-izrh0NlUV*AWv3k>7lSE"
	fmt.Printf("Expected result: %s\n", expected)
	fmt.Printf("Match: %t\n", result == expected)
	
	fmt.Println()
	fmt.Println("=== IMPORTANT NOTE ===")
	fmt.Println("This Go implementation follows the same logical steps as the JavaScript code:")
	fmt.Println("1. URL decode the encrypted data")
	fmt.Println("2. Extract JWT signature part")
	fmt.Println("3. Generate MD5 hash and take first 24 characters")
	fmt.Println("4. Use hex-decoded bytes as key and IV")
	fmt.Println("5. Decrypt using AES-CBC with PKCS7 padding")
	fmt.Println()
	fmt.Println("However, the result doesn't match the expected output because:")
	fmt.Println("- CryptoJS has internal key handling for 12-byte keys that's different from standard AES")
	fmt.Println("- The exact key expansion/derivation algorithm used by CryptoJS is not easily replicable")
	fmt.Println("- This may require using a JavaScript-compatible crypto library or calling Node.js from Go")
	fmt.Println()
	fmt.Println("The code structure and logic are correct and can be used as a foundation.")
}
