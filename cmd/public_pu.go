package main

import (
	"api-m/common"
	"api-m/handlers"
	"api-m/heplers/utils"
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/cors"

	_ "net/http/pprof"
)

func main() {
	port := flag.Int("port", 80, "Port run")
	m := mux.NewRouter()
	g := m.PathPrefix(common.PrefixPathPbuser).Subrouter()
	g.HandleFunc("/api/v1.0/ping", handlers.PingService).Methods(common.MethodGET)
	m.HandleFunc("/api/v1.0/ping", handlers.PingService).Methods(common.MethodGET)

	var newHandlePublicPu = http.HandlerFunc(
		func(rw http.ResponseWriter, r *http.Request) {
			handlers.PublicPu(rw, r)
		})
	g.<PERSON>("/{realPath:.+}", http.StripPrefix(common.PrefixPathPbuser, newHandlePublicPu))
	flag.Parse()
	go func() {
		err := http.ListenAndServe("localhost:6060", nil)
		if err != nil {
			common.LogConfig.Errorf("Start server error :: %v", err.Error())
		}
	}()

	s := &http.Server{
		Addr:        fmt.Sprintf(":%v", *port),
		Handler:     cors.AllowAll().Handler(utils.AllowQuerySemicolons(utils.SetSessionHeader(utils.BuildResponse()(utils.LimitPerPage(m))))),
		IdleTimeout: 120 * time.Second,
		ReadTimeout: 30 * time.Second,
		//WriteTimeout: 30 * time.Second,
	}

	go func() {
		err := s.ListenAndServe()
		if err != nil {
			common.LogConfig.Errorf("Start server error :: %v", err.Error())
		}
	}()

	sigChan := make(chan os.Signal)
	signal.Notify(sigChan, os.Interrupt)
	signal.Notify(sigChan, os.Kill)

	sig := <-sigChan
	common.LogConfig.Debugf("sig: %v", sig)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	err := s.Shutdown(ctx)
	if err != nil {
		return
	}
}
