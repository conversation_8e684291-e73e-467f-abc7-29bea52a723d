package main

import (
	"api-m/common"
	"api-m/handlers"
	"api-m/heplers/utils"
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/cors"

	_ "net/http/pprof"
)

func main() {
	port := flag.Int("port", 80, "Port run")
	m := mux.NewRouter()

	g := m.PathPrefix(common.PrefixPathGuest).Subrouter()
	g.HandleFunc("/api/v1.0/ping", handlers.PingService).Methods(http.MethodGet)
	m.HandleFunc("/api/v1.0/ping", handlers.PingService).Methods(http.MethodGet)

	var newHandleGuest = http.HandlerFunc(
		func(rw http.ResponseWriter, r *http.Request) {
			handlers.Guest(rw, r)
		})
	g.<PERSON><PERSON>("/{realPath:.+}", http.StripPrefix(common.PrefixPathGuest, newHandleGuest))
	flag.Parse()

	common.LogConfig.Infof("Start run port :: %v", *port)
	server := &http.Server{
		Addr:        fmt.Sprintf(":%v", *port),
		Handler:     cors.AllowAll().Handler(utils.AllowQuerySemicolons(utils.SetSessionHeader(utils.BuildResponse()(utils.LimitPerPage(m))))),
		IdleTimeout: 120 * time.Second,
		ReadTimeout: 30 * time.Second,
		//WriteTimeout: 30 * time.Second,
	}

	go func() {
		common.LogConfig.Infof("Start run port :: %v", *port)
		err := http.ListenAndServe("localhost:6060", nil)
		if err != nil {
			common.LogConfig.Errorf("Start server error :: %v", err.Error())
		}
	}()

	go func() {
		common.LogConfig.Infof("Start run port :: %v", *port)
		err := server.ListenAndServe()
		if err != nil {
			common.LogConfig.Errorf("Start server error :: %v", err.Error())
		}
	}()

	sigChan := make(chan os.Signal)
	signal.Notify(sigChan, os.Interrupt)
	signal.Notify(sigChan, os.Kill)

	sig := <-sigChan
	common.LogConfig.Debugf("sig: %v", sig)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	err := server.Shutdown(ctx)
	if err != nil {
		return
	}
}
