package common

import (
	"api-m/config/env"
	logConfig "api-m/heplers/logger"
	"mime"
	"path/filepath"
	"sort"
)

const MaxMemoryUpload = 40 << 20

var LogConfig = logConfig.StartLogger().Logger

const (
	VmTypeLocal = "local"

	DefaultLang       = "vi"
	HeaderXMerchantId = "X-Merchant-ID"
	HeaderContentType = "Content-Type"

	BadRequest          = "bad_request"
	UnAuthentice        = "unauthentice"
	NotAllowed          = "not_allowed"
	NotFound            = "not_found"
	UnAuthorization     = "unauthorization"
	PemError            = "pem_error"
	InternalServerError = "internal_server_error"
	ServiceNotAvailable = "service_not_available"

	LimitPerPage = "limit_per_page"

	TypeForwardPublicPu = "public_pu"
	TypeForwardWeb      = "web"
	TypeForwardGuest    = "guest"
	TypeForwardPartner  = "partner"
)

const (
	MobioDeviceType  = "mobio-device-type"
	MobileDeviceType = "mobile"
)

const MobioSpecialSemicolons = "mobio_special_semicolons"
const KeyCacheInfoWebPushByTrackingCode = "market_place_cache_#%v#%v"

const (
	TypicallyBearer = "Bearer"
	TypicallyBasic  = "Basic"
	TypicallyDigest = "Digest"
)

const (
	AesCipherKey = "4d4f42494f5365637265744032303231"
	AesCipherIv  = "4d4f42494f5365637265744032303231"
)

const (
	PrefixPathPbuser  = "/pbuser"
	PrefixPathWeb     = "/"
	PrefixPathGuest   = "/guest"
	PrefixPathPartner = "/partner"
)

const (
	MethodGET = "GET"
)

const (
	RedisTypeCluster = "2"
)


const (
	VersionOne = "1"
	VersionTwo = "2"
)

type PathFolder struct {
	FolderConfigLanguage string
}

func SetConfigFolder() PathFolder {
	folderConfigLanguage := filepath.Join(env.GetApplicationConfig().PathFolder, "config/lang/language")
	result := PathFolder{FolderConfigLanguage: folderConfigLanguage}
	return result
}

type ServiceMap struct {
	Location    string
	ServiceName string
}

type CustomMimeType struct {
	Extension string
	MimeType  string
}

type ConfigActionPathGuestApi struct {
	IsCheck                          bool // Cần check path này không?
	IsVerifyToken                    bool // Cần verify token không?
	IsAccessTokenExpired             bool // Chấp nhận token hết hạn không?
	IsCheckInfoByTrackingCodeWebPush bool // Url này được get thông tin dựa vào tracking code hay không? Sẽ lấy theo key header: mobio-webpush-tracking-code
	IsAutoFilDataResponse            bool
}

func initConfigActionPathGuestApi(isCheck bool, isCheckInfoByTrackingCodeWebPush bool, isVerifyToken bool, isAccessTokenExpired bool, IsAutoFilDataResponse bool) ConfigActionPathGuestApi {
	return ConfigActionPathGuestApi{
		IsCheck:                          isCheck,
		IsVerifyToken:                    isVerifyToken,
		IsAccessTokenExpired:             isAccessTokenExpired,
		IsCheckInfoByTrackingCodeWebPush: isCheckInfoByTrackingCodeWebPush,
		IsAutoFilDataResponse:            IsAutoFilDataResponse,
	}
}

func GetServiceMap() []ServiceMap {
	CommonServiceMap := map[string]string{
		"^/?/?adm":                             "admin-app-api-web-service",
		"^/?/?adm/partner":                     "admin-app-api-partner-service",
		"^/?/?adm/mobile":                      "admin-app-api-mobile-service",
		"^/?/?adm/webhook":                     "admin-app-webhook-service",
		"^/?/?ads":                             "ads-app-api-service",
		"^/?/?audience":                        "audience-app-api-service",
		"^/?/?audience/api/v[0-9]+\\.[0-9]+":   "audience-v2-app-api-service",
		"^/?/?callcenter":                      "callcenter-app-api-service",
		"^/?/?chattool":                        "chattool-app-api-service",
		"^/?/?chattool-socket":                 "chattool-app-socket-service",
		"^/?/?chattool/static":                 "chattool-app-api-service",
		"^/?/?cmssamsung":                      "cmssamsung-app-api-service",
		"^/?/?company":                         "company-app-api-service",
		"^/?/?company/mobile":                  "company-app-api-mobile-service",
		"^/?/?company/partner":                 "company-app-api-partner-service",
		"^/?/?crm/api/v[0-9]+\\.[0-9]+/static": "loyalty-api-app-service",
		"^/?/?digienty/web":                    "wp-web-push-api-services",
		"^/?/?dnc":                             "dnc-app-api-service",
		"^/?/?dynamic-event":                   "dynamic-event-app-api-service",
		"^/?/?emk":                             "emk-app-api-service",
		"^/?/?events":                          "user-event-app-api-service",
		"^/?/?journey":                         "journey-builder-app-api-service",
		"^/?/?journey/call_module_other":       "journey-builder-app-call_module_other-service",
		"^/?/?journey/webhook":                 "journey-builder-app-callback-service",
		"^/?/?landingpage/api":                 "landingpage-app-api-service",
		"^/?/?landingpage/pages":               "landingpage-app-web-service",
		"^/?/?loyalty":                         "loyalty-api-app-service",
		"^/?/?loyalty/external":                "loyalty-api-external-service",
		"^/?/?loyalty/mobile":                  "loyalty-api-mobile-app-service",
		"^/?/?loyalty/transaction":             "loyalty-api-transaction-app-service",
		"^/?/?mailclient":                      "mail-client-app-api-service",
		"^/?/?media/api":                       "media-app-api-service",
		"^/?/?media/static":                    "media-app-api-download-service",
		"^/?/?mkt":                             "marketing-app-api-service",
		"^/?/?mkt/call_module_other":           "marketing-app-call_module_other-service",
		"^/?/?mkt/webhook":                     "marketing-app-callback-service",
		"^/?/?nm":                              "nm-app-api-service",
		"^/?/?nm/mobile/api":                   "nm-app-mobile-api-service",
		"^/?/?nm/api/v[0-9]+\\.[0-9]+/emails/reports":        "nm-app-web-api-service",
		"^/?/?^/?/?nm/api/v[0-9]+\\.[0-9]+/message/multiple": "nm-app-api-send-message-service",
		"^/?/?nm/api/v1.0/sessions":                          "nm-app-api-notification-service",
		"^/?/?nm/onpremise/api":                              "nm-app-api-onpremise-service",
		"^/?/?nm/onpremise/webhook":                          "nm-app-webhook-onpermise-service",
		"^/?/?nm/webhook":                                    "nm-app-webhook-service",
		"^/?/?note":                                          "note-app-api-service",
		"^/?/?note/mobile":                                   "note-mobile-app-api-service",
		"^/?/?product":                                       "product-app-api-service",
		"^/?/?product/mobile":                                "product-app-mobile-api-service",
		"^/?/?profiling/etl":                                 "profiling-etl-app-api-service",
		"^/?/?profiling/call_module_other":                   "profiling-v4-app-call_module_other-api-service",
		"^/?/?profiling/external":                            "profiling-v4-app-external-api-service",
		"^/?/?profiling/v[0-9]+\\.[0-9]+":                    "profiling-v4-app-api-service",
		"^/?/?rapporteur":                                    "mobio-report-app-api-service",
		"^/?/?sale":                                          "sale-api-app-service",
		"^/?/?sale/filter":                                   "sale-api-filter-app-service",
		"^/?/?sale/mobile":                                   "sale-api-mobile-app-service",
		"^/?/?sale/report":                                   "sale-api-report-app-service",
		"^/?/?shortenurl":                                    "shorten-url-manager-app-api-service",
		"^/?/?social/api":                                    "social-api-app-service",
		"^/?/?social/hub":                                    "social-listener-app-api-service",
		"^/?/?social/report":                                 "social-reports-app-service",
		"^/?/?social/static":                                 "social-api-app-service",
		"^/?/?social/webhook":                                "social-webhook-app-service",
		"^/?/?socket\\.io":                                   "nm-app-socket-service",
		"^/?/?survey":                                        "survey-app-api-service",
		"^/?/?tag":                                           "tag-app-api-service",
		"^/?/?task":                                          "task-management-app-api-service",
		"^/?/?task/mobile":                                   "task-management-app-mobile-api-service",
		"^/?/?ticket":                                        "ticket-app-api-service",
		"^/?/?voucher":                                       "voucher-app-service",
		"^/?/?voucher/external":                              "voucher-app-external-service",
		"^/?/?voucher/mobile":                                "voucher-app-mobile-service",
		"^/?/?template/api":                                  "mobio-template-app-api-service",
		"^/?/?monitor/api":                                   "monitor-system-app-api-service",
		"^/?/?datasync/api":                                  "data-out-app-api-service",
		"^/?/?mcc-merchant/api":                              "mcc-merchant-app-api-service",
		"^/?/?location/api":                                  "location-app-api-service",
		"^/?/?location/call_module_other/api":                "location-app-api-call_module_other-service",
		"^/?/?location/partner/api":                          "location-app-api-partner-service",
		"^/?/?location/mobile/api":                           "location-app-api-mobile-service",
		"^/?/?workflow/api":                                  "workflow-app-api-service",
		"^/?/?workflow/internal/api":                         "workflow-app-internal-service",
		"^/?/?workflow/report/api":                           "workflow-app-report-service",
		"^/?/?segment/api":                                   "segment-app-api-service",
		"^/?/?segment/internal/api":                          "segment-app-api-internal-service",
		"^/?/?segment/audience/api":                          "audience-segment-app-api-service",
		"^/?/?segment/audience/internal/api":                 "audience-segment-app-internal-service",
		"^/?/?audience-segment/api":                          "audience-segment-app-api-service",
		"^/?/?audience-segment/internal/api":                 "audience-segment-app-internal-service",
		"^/?/?comment/api":                                   "comment-management-app-api-service",
		"^/?/?sale-memo/api":                                 "sale-memo-app-api-service",
		"^/?/?/sale-memo/external":                           "sale-memo-app-api-external-service",
		"^/?/?/transaction/external":                         "transaction-hub-app-api-external-service",
		"^/?/?/market-place/api":                             "market-place-app-api-service",
		"^/?/?/market-place/report/api":                      "market-place-app-api-service",
		"^/?/?/market-place/external/api":                    "marketplace-app-api-external-service",
		"^/?/?/onpage-journey/api":                           "onpage-journey-app-api-service",
		"^/?/?/onpage-journey/internal/api":                  "onpage-journey-app-internal-service",
		"^/?/?/onpage-journey/report/api":                    "onpage-journey-app-report-service",
		"^/?/?/kpi-management/api":                           "kpi-management-app-api-service",
		"^/?/?/kpi-management/mobile/api":                    "kpi-management-app-api-mobile-service",
		"^/?/?/kpi-management/external/api":                  "kpi-management-app-api-external-service",
		"^/?/?/kpi-management/report/api":                    "kpi-management-app-api-report-service",
		"^/?/?/dpmt/api/":                                    "data-pipeline-management-tool-app-api-service",
		"^/?/?/data_dictionary/api":                          "data-dictionary-app-api-service",
		"^/?/?/data_dictionary/internal/api":                 "data-dictionary-app-api-internal-service",
		"^/?/?/onpage-journey/sdk/api/":                      "onpage-journey-app-sdk-service",
		"^/?/?/profiling-report/api/":                        "profilingreport-api-web-service",
		"^/?/?/mobilebackend/api/":                           "mobilebackend-api-service",
		"^/?/?/form/api/":                                    "form-api-service",
		"^/?/?ticket/mobile":                                 "ticket-api-mobile-app-service",
		"^/?/?comment/mobile":                                "comment-api-mobile-app-service",
		"^/?/?knowledge-base/api/":                           "kbm-api-app-service",
		"^/?/?knowledge-base/external/api/":                  "kbm-external-api-app-service",
		"^/?/?knowledge-base/external/mobile/api/":           "kbm-external-api-app-service",
		"^/?/?/payment/external/api/":                        "payment-api-external-service",
		"^/?/?sale/external/":                                "sale-api-external-service",
		"^/?/?/master-data/api/":                             "masterdata-api-web-service",
		"^/?/?/wfb/api/":                                     "wfb-app-api-service",
		"^/?/?/wfb/mobile/api/":                              "wfb-mobile-app-api-service",
		"^/?/?/wfb/get-data/api/":                            "wfb-get-data-app-api-service",
		"^/?/?/wfb/mobile/get-data/api/":                     "wfb-mobile-get-data-app-api-service",
		"^/?/?/calculation-attribute/api/":                   "calculationattribute-api-app-service",
		"^/?/?/mobilebackend/external/":                      "mobilebackend-api-external-service",
		"^/?/?/wfb/mobile/get-data/":                         "wfb-mobile-get-data-app-api-service",
		"^/?/?/wfb/partner/":                                 "wfb-partner-app-api-service",
		"^/?/?/mobilebackend/mockup-data/api/":               "mobilebackendmockup-api-service",
		"^/?/?/data-mgt/api/":                                "data-mgt-app-api-service",
		"^/?/?/communicationhub/external/api/":               "communicationhub-api-external-service",
		"^/?/?/wfb/mock-data/api/":                           "wfb-mock-api-service",
		"^/?/?/wfb/external/api/":                            "wfb-api-external-service",
	}
	var result []ServiceMap

	for k, v := range CommonServiceMap {
		result = append(result, ServiceMap{k, v})
	}

	sort.Slice(result, func(i, j int) bool {
		return len(result[i].Location) > len(result[j].Location)
	})

	return result
}

func IntCustomMimeType() {
	commonCustomMimeType := map[string]string{
		".js":               "application/javascript",
		".mjs":              "application/javascript",
		".json":             "application/json",
		".webmanifest":      "application/manifest+json",
		".doc":              "application/msword",
		".dot":              "application/msword",
		".wiz":              "application/msword",
		".bin":              "application/octet-stream",
		".a":                "application/octet-stream",
		".dll":              "application/x-msdownload",
		".exe":              "application/x-msdownload",
		".o":                "application/octet-stream",
		".obj":              "application/x-tgif",
		".so":               "application/octet-stream",
		".oda":              "application/oda",
		".pdf":              "application/pdf",
		".p7c":              "application/pkcs7-mime",
		".ps":               "application/postscript",
		".ai":               "application/postscript",
		".eps":              "application/postscript",
		".m3u":              "audio/x-mpegurl",
		".m3u8":             "application/x-mpegurl",
		".xls":              "application/vnd.ms-excel",
		".xlb":              "application/vnd.ms-excel",
		".ppt":              "application/vnd.ms-powerpoint",
		".pot":              "application/vnd.ms-powerpoint",
		".ppa":              "application/vnd.ms-powerpoint",
		".pps":              "application/vnd.ms-powerpoint",
		".pwz":              "application/vnd.ms-powerpoint",
		".wasm":             "application/wasm",
		".bcpio":            "application/x-bcpio",
		".cpio":             "application/x-cpio",
		".csh":              "application/x-csh",
		".dvi":              "application/x-dvi",
		".gtar":             "application/x-gtar",
		".hdf":              "application/x-hdf",
		".h5":               "application/x-hdf5",
		".latex":            "application/x-latex",
		".mif":              "application/vnd.mif",
		".cdf":              "application/x-netcdf",
		".nc":               "application/x-netcdf",
		".p12":              "application/x-pkcs12",
		".pfx":              "application/x-pkcs12",
		".ram":              "audio/x-pn-realaudio",
		".pyc":              "application/x-python-code",
		".pyo":              "application/x-python-code",
		".sh":               "application/x-sh",
		".shar":             "application/x-shar",
		".swf":              "application/x-shockwave-flash",
		".sv4cpio":          "application/x-sv4cpio",
		".sv4crc":           "application/x-sv4crc",
		".tar":              "application/x-tar",
		".tcl":              "application/x-tcl",
		".tex":              "application/x-tex",
		".texi":             "application/x-texinfo",
		".texinfo":          "application/x-texinfo",
		".roff":             "text/troff",
		".t":                "text/troff",
		".tr":               "text/troff",
		".man":              "text/troff",
		".me":               "text/troff",
		".ms":               "text/troff",
		".ustar":            "application/x-ustar",
		".src":              "application/x-wais-source",
		".xsl":              "application/xml",
		".rdf":              "application/rdf+xml",
		".wsdl":             "application/wsdl+xml",
		".xpdl":             "application/xml",
		".zip":              "application/zip",
		".au":               "audio/basic",
		".snd":              "audio/basic",
		".mp3":              "audio/mpeg",
		".mp2":              "audio/mpeg",
		".aif":              "audio/x-aiff",
		".aifc":             "audio/x-aiff",
		".aiff":             "audio/x-aiff",
		".ra":               "audio/x-pn-realaudio",
		".wav":              "audio/x-wav",
		".bmp":              "image/bmp",
		".gif":              "image/gif",
		".ief":              "image/ief",
		".jpg":              "image/jpeg",
		".jpe":              "image/jpeg",
		".jpeg":             "image/jpeg",
		".png":              "image/png",
		".svg":              "image/svg+xml",
		".tiff":             "image/tiff",
		".tif":              "image/tiff",
		".ico":              "image/x-icon",
		".ras":              "image/x-cmu-raster",
		".pnm":              "image/x-portable-anymap",
		".pbm":              "image/x-portable-bitmap",
		".pgm":              "image/x-portable-graymap",
		".ppm":              "image/x-portable-pixmap",
		".rgb":              "image/x-rgb",
		".xbm":              "image/x-xbitmap",
		".xpm":              "image/x-xpixmap",
		".xwd":              "image/x-xwindowdump",
		".eml":              "message/rfc822",
		".mht":              "message/rfc822",
		".mhtml":            "message/rfc822",
		".nws":              "message/rfc822",
		".css":              "text/css",
		".csv":              "text/csv",
		".html":             "text/html",
		".htm":              "text/html",
		".txt":              "text/plain",
		".bat":              "application/x-msdownload",
		".c":                "text/x-c",
		".h":                "text/x-c",
		".ksh":              "text/plain",
		".pl":               "text/plain",
		".rtx":              "text/richtext",
		".tsv":              "text/tab-separated-values",
		".py":               "text/x-python",
		".etx":              "text/x-setext",
		".sgm":              "text/sgml",
		".sgml":             "text/sgml",
		".vcf":              "text/x-vcard",
		".xml":              "application/xml",
		".mp4":              "video/mp4",
		".mpeg":             "video/mpeg",
		".m1v":              "video/mpeg",
		".mpa":              "video/mpeg",
		".mpe":              "video/mpeg",
		".mpg":              "video/mpeg",
		".mov":              "video/quicktime",
		".qt":               "video/quicktime",
		".webm":             "video/webm",
		".avi":              "video/x-msvideo",
		".movie":            "video/x-sgi-movie",
		".ez":               "application/andrew-inset",
		".aw":               "application/applixware",
		".atom":             "application/atom+xml",
		".atomcat":          "application/atomcat+xml",
		".atomsvc":          "application/atomsvc+xml",
		".ccxml":            "application/ccxml+xml",
		".cdmia":            "application/cdmi-capability",
		".cdmic":            "application/cdmi-container",
		".cdmid":            "application/cdmi-domain",
		".cdmio":            "application/cdmi-object",
		".cdmiq":            "application/cdmi-queue",
		".cu":               "application/cu-seeme",
		".davmount":         "application/davmount+xml",
		".dbk":              "application/docbook+xml",
		".dssc":             "application/dssc+der",
		".xdssc":            "application/dssc+xml",
		".ecma":             "application/ecmascript",
		".emma":             "application/emma+xml",
		".epub":             "application/epub+zip",
		".exi":              "application/exi",
		".pfr":              "application/font-tdpfr",
		".gml":              "application/gml+xml",
		".gpx":              "application/gpx+xml",
		".gxf":              "application/gxf",
		".stk":              "application/hyperstudio",
		".ink":              "application/inkml+xml",
		".inkml":            "application/inkml+xml",
		".ipfix":            "application/ipfix",
		".jar":              "application/java-archive",
		".ser":              "application/java-serialized-object",
		".class":            "application/java-vm",
		".jsonml":           "application/jsonml+json",
		".lostxml":          "application/lost+xml",
		".hqx":              "application/mac-binhex40",
		".cpt":              "application/mac-compactpro",
		".mads":             "application/mads+xml",
		".mrc":              "application/marc",
		".mrcx":             "application/marcxml+xml",
		".ma":               "application/mathematica",
		".nb":               "application/mathematica",
		".mb":               "application/mathematica",
		".mathml":           "application/mathml+xml",
		".mbox":             "application/mbox",
		".mscml":            "application/mediaservercontrol+xml",
		".metalink":         "application/metalink+xml",
		".meta4":            "application/metalink4+xml",
		".mets":             "application/mets+xml",
		".mods":             "application/mods+xml",
		".m21":              "application/mp21",
		".mp21":             "application/mp21",
		".mp4s":             "application/mp4",
		".mxf":              "application/mxf",
		".dms":              "application/octet-stream",
		".lrf":              "application/octet-stream",
		".mar":              "application/octet-stream",
		".dist":             "application/octet-stream",
		".distz":            "application/octet-stream",
		".pkg":              "application/octet-stream",
		".bpk":              "application/octet-stream",
		".dump":             "application/octet-stream",
		".elc":              "application/octet-stream",
		".deploy":           "application/octet-stream",
		".mobipocket-ebook": "application/octet-stream",
		".opf":              "application/oebps-package+xml",
		".ogx":              "application/ogg",
		".omdoc":            "application/omdoc+xml",
		".onetoc":           "application/onenote",
		".onetoc2":          "application/onenote",
		".onetmp":           "application/onenote",
		".onepkg":           "application/onenote",
		".oxps":             "application/oxps",
		".xer":              "application/patch-ops-error+xml",
		".pgp":              "application/pgp-encrypted",
		".asc":              "application/pgp-signature",
		".sig":              "application/pgp-signature",
		".prf":              "application/pics-rules",
		".p10":              "application/pkcs10",
		".p7m":              "application/pkcs7-mime",
		".p7s":              "application/pkcs7-signature",
		".p8":               "application/pkcs8",
		".ac":               "application/pkix-attr-cert",
		".cer":              "application/pkix-cert",
		".crl":              "application/pkix-crl",
		".pkipath":          "application/pkix-pkipath",
		".pki":              "application/pkixcmp",
		".pls":              "application/pls+xml",
		".cww":              "application/prs.cww",
		".pskcxml":          "application/pskc+xml",
		".rif":              "application/reginfo+xml",
		".rnc":              "application/relax-ng-compact-syntax",
		".rl":               "application/resource-lists+xml",
		".rld":              "application/resource-lists-diff+xml",
		".rs":               "application/rls-services+xml",
		".gbr":              "application/rpki-ghostbusters",
		".mft":              "application/rpki-manifest",
		".roa":              "application/rpki-roa",
		".rsd":              "application/rsd+xml",
		".rss":              "application/rss+xml",
		".rtf":              "application/rtf",
		".sbml":             "application/sbml+xml",
		".scq":              "application/scvp-cv-request",
		".scs":              "application/scvp-cv-response",
		".spq":              "application/scvp-vp-request",
		".spp":              "application/scvp-vp-response",
		".sdp":              "application/sdp",
		".setpay":           "application/set-payment-initiation",
		".setreg":           "application/set-registration-initiation",
		".shf":              "application/shf+xml",
		".smi":              "application/smil+xml",
		".smil":             "application/smil+xml",
		".rq":               "application/sparql-query",
		".srx":              "application/sparql-results+xml",
		".gram":             "application/srgs",
		".grxml":            "application/srgs+xml",
		".sru":              "application/sru+xml",
		".ssdl":             "application/ssdl+xml",
		".ssml":             "application/ssml+xml",
		".tei":              "application/tei+xml",
		".teicorpus":        "application/tei+xml",
		".tfi":              "application/thraud+xml",
		".tsd":              "application/timestamped-data",
		".plb":              "application/vnd.3gpp.pic-bw-large",
		".psb":              "application/vnd.3gpp.pic-bw-small",
		".pvb":              "application/vnd.3gpp.pic-bw-var",
		".tcap":             "application/vnd.3gpp2.tcap",
		".pwn":              "application/vnd.3m.post-it-notes",
		".aso":              "application/vnd.accpac.simply.aso",
		".imp":              "application/vnd.accpac.simply.imp",
		".acu":              "application/vnd.acucobol",
		".atc":              "application/vnd.acucorp",
		".acutc":            "application/vnd.acucorp",
		".air":              "application/vnd.adobe.air-application-installer-package+zip",
		".fcdt":             "application/vnd.adobe.formscentral.fcdt",
		".fxp":              "application/vnd.adobe.fxp",
		".fxpl":             "application/vnd.adobe.fxp",
		".xdp":              "application/vnd.adobe.xdp+xml",
		".xfdf":             "application/vnd.adobe.xfdf",
		".ahead":            "application/vnd.ahead.space",
		".azf":              "application/vnd.airzip.filesecure.azf",
		".azs":              "application/vnd.airzip.filesecure.azs",
		".azw":              "application/vnd.amazon.ebook",
		".acc":              "application/vnd.americandynamics.acc",
		".ami":              "application/vnd.amiga.ami",
		".apk":              "application/vnd.android.package-archive",
		".cii":              "application/vnd.anser-web-certificate-issue-initiation",
		".fti":              "application/vnd.anser-web-funds-transfer-initiation",
		".atx":              "application/vnd.antix.game-component",
		".mpkg":             "application/vnd.apple.installer+xml",
		".swi":              "application/vnd.aristanetworks.swi",
		".iota":             "application/vnd.astraea-software.iota",
		".aep":              "application/vnd.audiograph",
		".mpm":              "application/vnd.blueice.multipass",
		".bmi":              "application/vnd.bmi",
		".rep":              "application/vnd.businessobjects",
		".cdxml":            "application/vnd.chemdraw+xml",
		".mmd":              "application/vnd.chipnuts.karaoke-mmd",
		".cdy":              "application/vnd.cinderella",
		".cla":              "application/vnd.claymore",
		".rp9":              "application/vnd.cloanto.rp9",
		".c4g":              "application/vnd.clonk.c4group",
		".c4d":              "application/vnd.clonk.c4group",
		".c4f":              "application/vnd.clonk.c4group",
		".c4p":              "application/vnd.clonk.c4group",
		".c4u":              "application/vnd.clonk.c4group",
		".c11amc":           "application/vnd.cluetrust.cartomobile-config",
		".c11amz":           "application/vnd.cluetrust.cartomobile-config-pkg",
		".csp":              "application/vnd.commonspace",
		".cdbcmsg":          "application/vnd.contact.cmsg",
		".cmc":              "application/vnd.cosmocaller",
		".clkx":             "application/vnd.crick.clicker",
		".clkk":             "application/vnd.crick.clicker.keyboard",
		".clkp":             "application/vnd.crick.clicker.palette",
		".clkt":             "application/vnd.crick.clicker.template",
		".clkw":             "application/vnd.crick.clicker.wordbank",
		".wbs":              "application/vnd.criticaltools.wbs+xml",
		".pml":              "application/vnd.ctc-posml",
		".ppd":              "application/vnd.cups-ppd",
		".car":              "application/vnd.curl.car",
		".pcurl":            "application/vnd.curl.pcurl",
		".dart":             "application/vnd.dart",
		".rdz":              "application/vnd.data-vision.rdz",
		".uvf":              "application/vnd.dece.data",
		".uvvf":             "application/vnd.dece.data",
		".uvd":              "application/vnd.dece.data",
		".uvvd":             "application/vnd.dece.data",
		".uvt":              "application/vnd.dece.ttml+xml",
		".uvvt":             "application/vnd.dece.ttml+xml",
		".uvx":              "application/vnd.dece.unspecified",
		".uvvx":             "application/vnd.dece.unspecified",
		".uvz":              "application/vnd.dece.zip",
		".uvvz":             "application/vnd.dece.zip",
		".fe_launch":        "application/vnd.denovo.fcselayout-link",
		".dna":              "application/vnd.dna",
		".mlp":              "application/vnd.dolby.mlp",
		".dpg":              "application/vnd.dpgraph",
		".dfac":             "application/vnd.dreamfactory",
		".kpxx":             "application/vnd.ds-keypoint",
		".ait":              "application/vnd.dvb.ait",
		".svc":              "application/vnd.dvb.service",
		".geo":              "application/vnd.dynageo",
		".mag":              "application/vnd.ecowin.chart",
		".nml":              "application/vnd.enliven",
		".esf":              "application/vnd.epson.esf",
		".msf":              "application/vnd.epson.msf",
		".qam":              "application/vnd.epson.quickanime",
		".slt":              "application/vnd.epson.salt",
		".ssf":              "application/vnd.epson.ssf",
		".es3":              "application/vnd.eszigno3+xml",
		".et3":              "application/vnd.eszigno3+xml",
		".ez2":              "application/vnd.ezpix-album",
		".ez3":              "application/vnd.ezpix-package",
		".fdf":              "application/vnd.fdf",
		".mseed":            "application/vnd.fdsn.mseed",
		".seed":             "application/vnd.fdsn.seed",
		".dataless":         "application/vnd.fdsn.seed",
		".gph":              "application/vnd.flographit",
		".ftc":              "application/vnd.fluxtime.clip",
		".fm":               "application/vnd.framemaker",
		".frame":            "application/vnd.framemaker",
		".maker":            "application/vnd.framemaker",
		".book":             "application/vnd.framemaker",
		".fnc":              "application/vnd.frogans.fnc",
		".ltf":              "application/vnd.frogans.ltf",
		".fsc":              "application/vnd.fsc.weblaunch",
		".oas":              "application/vnd.fujitsu.oasys",
		".oa2":              "application/vnd.fujitsu.oasys2",
		".oa3":              "application/vnd.fujitsu.oasys3",
		".fg5":              "application/vnd.fujitsu.oasysgp",
		".bh2":              "application/vnd.fujitsu.oasysprs",
		".ddd":              "application/vnd.fujixerox.ddd",
		".xdw":              "application/vnd.fujixerox.docuworks",
		".xbd":              "application/vnd.fujixerox.docuworks.binder",
		".fzs":              "application/vnd.fuzzysheet",
		".txd":              "application/vnd.genomatix.tuxedo",
		".ggb":              "application/vnd.geogebra.file",
		".ggt":              "application/vnd.geogebra.tool",
		".gex":              "application/vnd.geometry-explorer",
		".gre":              "application/vnd.geometry-explorer",
		".gxt":              "application/vnd.geonext",
		".g2w":              "application/vnd.geoplan",
		".g3w":              "application/vnd.geospace",
		".gmx":              "application/vnd.gmx",
		".kml":              "application/vnd.google-earth.kml+xml",
		".kmz":              "application/vnd.google-earth.kmz",
		".gqf":              "application/vnd.grafeq",
		".gqs":              "application/vnd.grafeq",
		".gac":              "application/vnd.groove-account",
		".ghf":              "application/vnd.groove-help",
		".gim":              "application/vnd.groove-identity-message",
		".grv":              "application/vnd.groove-injector",
		".gtm":              "application/vnd.groove-tool-message",
		".tpl":              "application/vnd.groove-tool-template",
		".vcg":              "application/vnd.groove-vcard",
		".hal":              "application/vnd.hal+xml",
		".zmm":              "application/vnd.handheld-entertainment+xml",
		".hbci":             "application/vnd.hbci",
		".les":              "application/vnd.hhe.lesson-player",
		".hpgl":             "application/vnd.hp-hpgl",
		".hpid":             "application/vnd.hp-hpid",
		".hps":              "application/vnd.hp-hps",
		".jlt":              "application/vnd.hp-jlyt",
		".pcl":              "application/vnd.hp-pcl",
		".pclxl":            "application/vnd.hp-pclxl",
		".sfd-hdstx":        "application/vnd.hydrostatix.sof-data",
		".mpy":              "application/vnd.ibm.minipay",
		".afp":              "application/vnd.ibm.modcap",
		".listafp":          "application/vnd.ibm.modcap",
		".list3820":         "application/vnd.ibm.modcap",
		".irm":              "application/vnd.ibm.rights-management",
		".sc":               "application/vnd.ibm.secure-container",
		".icc":              "application/vnd.iccprofile",
		".icm":              "application/vnd.iccprofile",
		".igl":              "application/vnd.igloader",
		".ivp":              "application/vnd.immervision-ivp",
		".ivu":              "application/vnd.immervision-ivu",
		".igm":              "application/vnd.insors.igm",
		".xpw":              "application/vnd.intercon.formnet",
		".xpx":              "application/vnd.intercon.formnet",
		".i2g":              "application/vnd.intergeo",
		".qbo":              "application/vnd.intu.qbo",
		".qfx":              "application/vnd.intu.qfx",
		".rcprofile":        "application/vnd.ipunplugged.rcprofile",
		".irp":              "application/vnd.irepository.package+xml",
		".xpr":              "application/vnd.is-xpr",
		".fcs":              "application/vnd.isac.fcs",
		".jam":              "application/vnd.jam",
		".rms":              "application/vnd.jcp.javame.midlet-rms",
		".jisp":             "application/vnd.jisp",
		".joda":             "application/vnd.joost.joda-archive",
		".ktz":              "application/vnd.kahootz",
		".ktr":              "application/vnd.kahootz",
		".karbon":           "application/vnd.kde.karbon",
		".chrt":             "application/vnd.kde.kchart",
		".kfo":              "application/vnd.kde.kformula",
		".flw":              "application/vnd.kde.kivio",
		".kon":              "application/vnd.kde.kontour",
		".kpr":              "application/vnd.kde.kpresenter",
		".kpt":              "application/vnd.kde.kpresenter",
		".ksp":              "application/vnd.kde.kspread",
		".kwd":              "application/vnd.kde.kword",
		".kwt":              "application/vnd.kde.kword",
		".htke":             "application/vnd.kenameaapp",
		".kia":              "application/vnd.kidspiration",
		".kne":              "application/vnd.kinar",
		".knp":              "application/vnd.kinar",
		".skp":              "application/vnd.koan",
		".skd":              "application/vnd.koan",
		".skt":              "application/vnd.koan",
		".skm":              "application/vnd.koan",
		".sse":              "application/vnd.kodak-descriptor",
		".lasxml":           "application/vnd.las.las+xml",
		".lbd":              "application/vnd.llamagraphics.life-balance.desktop",
		".lbe":              "application/vnd.llamagraphics.life-balance.exchange+xml",
		".123":              "application/vnd.lotus-1-2-3",
		".apr":              "application/vnd.lotus-approach",
		".pre":              "application/vnd.lotus-freelance",
		".nsf":              "application/vnd.lotus-notes",
		".org":              "application/vnd.lotus-organizer",
		".scm":              "application/vnd.lotus-screencam",
		".lwp":              "application/vnd.lotus-wordpro",
		".portpkg":          "application/vnd.macports.portpkg",
		".mcd":              "application/vnd.mcd",
		".mc1":              "application/vnd.medcalcdata",
		".cdkey":            "application/vnd.mediastation.cdkey",
		".mwf":              "application/vnd.mfer",
		".mfm":              "application/vnd.mfmp",
		".flo":              "application/vnd.micrografx.flo",
		".igx":              "application/vnd.micrografx.igx",
		".daf":              "application/vnd.mobius.daf",
		".dis":              "application/vnd.mobius.dis",
		".mbk":              "application/vnd.mobius.mbk",
		".mqy":              "application/vnd.mobius.mqy",
		".msl":              "application/vnd.mobius.msl",
		".plc":              "application/vnd.mobius.plc",
		".txf":              "application/vnd.mobius.txf",
		".mpn":              "application/vnd.mophun.application",
		".mpc":              "application/vnd.mophun.certificate",
		".xul":              "application/vnd.mozilla.xul+xml",
		".cil":              "application/vnd.ms-artgalry",
		".cab":              "application/vnd.ms-cab-compressed",
		".xlm":              "application/vnd.ms-excel",
		".xla":              "application/vnd.ms-excel",
		".xlc":              "application/vnd.ms-excel",
		".xlt":              "application/vnd.ms-excel",
		".xlw":              "application/vnd.ms-excel",
		".xlam":             "application/vnd.ms-excel.addin.macroenabled.12",
		".xlsb":             "application/vnd.ms-excel.sheet.binary.macroenabled.12",
		".xlsm":             "application/vnd.ms-excel.sheet.macroenabled.12",
		".xltm":             "application/vnd.ms-excel.template.macroenabled.12",
		".eot":              "application/vnd.ms-fontobject",
		".chm":              "application/vnd.ms-htmlhelp",
		".ims":              "application/vnd.ms-ims",
		".lrm":              "application/vnd.ms-lrm",
		".thmx":             "application/vnd.ms-officetheme",
		".cat":              "application/vnd.ms-pki.seccat",
		".stl":              "application/vnd.ms-pki.stl",
		".ppam":             "application/vnd.ms-powerpoint.addin.macroenabled.12",
		".pptm":             "application/vnd.ms-powerpoint.presentation.macroenabled.12",
		".sldm":             "application/vnd.ms-powerpoint.slide.macroenabled.12",
		".ppsm":             "application/vnd.ms-powerpoint.slideshow.macroenabled.12",
		".potm":             "application/vnd.ms-powerpoint.template.macroenabled.12",
		".mpp":              "application/vnd.ms-project",
		".mpt":              "application/vnd.ms-project",
		".docm":             "application/vnd.ms-word.document.macroenabled.12",
		".dotm":             "application/vnd.ms-word.template.macroenabled.12",
		".wps":              "application/vnd.ms-works",
		".wks":              "application/vnd.ms-works",
		".wcm":              "application/vnd.ms-works",
		".wdb":              "application/vnd.ms-works",
		".wpl":              "application/vnd.ms-wpl",
		".xps":              "application/vnd.ms-xpsdocument",
		".mseq":             "application/vnd.mseq",
		".mus":              "application/vnd.musician",
		".msty":             "application/vnd.muvee.style",
		".taglet":           "application/vnd.mynfc",
		".nlu":              "application/vnd.neurolanguage.nlu",
		".ntf":              "application/vnd.nitf",
		".nitf":             "application/vnd.nitf",
		".nnd":              "application/vnd.noblenet-directory",
		".nns":              "application/vnd.noblenet-sealer",
		".nnw":              "application/vnd.noblenet-web",
		".ngdat":            "application/vnd.nokia.n-gage.data",
		".n-gage":           "application/vnd.nokia.n-gage.symbian.install",
		".rpst":             "application/vnd.nokia.radio-preset",
		".rpss":             "application/vnd.nokia.radio-presets",
		".edm":              "application/vnd.novadigm.edm",
		".edx":              "application/vnd.novadigm.edx",
		".ext":              "application/vnd.novadigm.ext",
		".odc":              "application/vnd.oasis.opendocument.chart",
		".otc":              "application/vnd.oasis.opendocument.chart-template",
		".odb":              "application/vnd.oasis.opendocument.database",
		".odf":              "application/vnd.oasis.opendocument.formula",
		".odft":             "application/vnd.oasis.opendocument.formula-template",
		".odg":              "application/vnd.oasis.opendocument.graphics",
		".otg":              "application/vnd.oasis.opendocument.graphics-template",
		".odi":              "application/vnd.oasis.opendocument.image",
		".oti":              "application/vnd.oasis.opendocument.image-template",
		".odp":              "application/vnd.oasis.opendocument.presentation",
		".otp":              "application/vnd.oasis.opendocument.presentation-template",
		".ods":              "application/vnd.oasis.opendocument.spreadsheet",
		".ots":              "application/vnd.oasis.opendocument.spreadsheet-template",
		".odt":              "application/vnd.oasis.opendocument.text",
		".odm":              "application/vnd.oasis.opendocument.text-master",
		".ott":              "application/vnd.oasis.opendocument.text-template",
		".oth":              "application/vnd.oasis.opendocument.text-web",
		".xo":               "application/vnd.olpc-sugar",
		".dd2":              "application/vnd.oma.dd2+xml",
		".oxt":              "application/vnd.openofficeorg.extension",
		".pptx":             "application/vnd.openxmlformats-officedocument.presentationml.presentation",
		".sldx":             "application/vnd.openxmlformats-officedocument.presentationml.slide",
		".ppsx":             "application/vnd.openxmlformats-officedocument.presentationml.slideshow",
		".potx":             "application/vnd.openxmlformats-officedocument.presentationml.template",
		".xlsx":             "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		".xltx":             "application/vnd.openxmlformats-officedocument.spreadsheetml.template",
		".docx":             "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
		".dotx":             "application/vnd.openxmlformats-officedocument.wordprocessingml.template",
		".mgp":              "application/vnd.osgeo.mapguide.package",
		".dp":               "application/vnd.osgi.dp",
		".esa":              "application/vnd.osgi.subsystem",
		".pdb":              "application/vnd.palm",
		".pqa":              "application/vnd.palm",
		".oprc":             "application/vnd.palm",
		".paw":              "application/vnd.pawaafile",
		".str":              "application/vnd.pg.format",
		".ei6":              "application/vnd.pg.osasli",
		".efif":             "application/vnd.picsel",
		".wg":               "application/vnd.pmi.widget",
		".plf":              "application/vnd.pocketlearn",
		".pbd":              "application/vnd.powerbuilder6",
		".box":              "application/vnd.previewsystems.box",
		".mgz":              "application/vnd.proteus.magazine",
		".qps":              "application/vnd.publishare-delta-tree",
		".ptid":             "application/vnd.pvi.ptid1",
		".qxd":              "application/vnd.quark.quarkxpress",
		".qxt":              "application/vnd.quark.quarkxpress",
		".qwd":              "application/vnd.quark.quarkxpress",
		".qwt":              "application/vnd.quark.quarkxpress",
		".qxl":              "application/vnd.quark.quarkxpress",
		".qxb":              "application/vnd.quark.quarkxpress",
		".bed":              "application/vnd.realvnc.bed",
		".mxl":              "application/vnd.recordare.musicxml",
		".musicxml":         "application/vnd.recordare.musicxml+xml",
		".cryptonote":       "application/vnd.rig.cryptonote",
		".cod":              "application/vnd.rim.cod",
		".rm":               "application/vnd.rn-realmedia",
		".rmvb":             "application/vnd.rn-realmedia-vbr",
		".link66":           "application/vnd.route66.link66+xml",
		".st":               "application/vnd.sailingtracker.track",
		".see":              "application/vnd.seemail",
		".sema":             "application/vnd.sema",
		".semd":             "application/vnd.semd",
		".semf":             "application/vnd.semf",
		".ifm":              "application/vnd.shana.informed.formdata",
		".itp":              "application/vnd.shana.informed.formtemplate",
		".iif":              "application/vnd.shana.informed.interchange",
		".ipk":              "application/vnd.shana.informed.package",
		".twd":              "application/vnd.simtech-mindmapper",
		".twds":             "application/vnd.simtech-mindmapper",
		".mmf":              "application/vnd.smaf",
		".teacher":          "application/vnd.smart.teacher",
		".sdkm":             "application/vnd.solent.sdkm+xml",
		".sdkd":             "application/vnd.solent.sdkm+xml",
		".dxp":              "application/vnd.spotfire.dxp",
		".sfs":              "application/vnd.spotfire.sfs",
		".sdc":              "application/vnd.stardivision.calc",
		".sda":              "application/vnd.stardivision.draw",
		".sdd":              "application/vnd.stardivision.impress",
		".smf":              "application/vnd.stardivision.math",
		".sdw":              "application/vnd.stardivision.writer",
		".vor":              "application/vnd.stardivision.writer",
		".sgl":              "application/vnd.stardivision.writer-global",
		".smzip":            "application/vnd.stepmania.package",
		".sm":               "application/vnd.stepmania.stepchart",
		".sxc":              "application/vnd.sun.xml.calc",
		".stc":              "application/vnd.sun.xml.calc.template",
		".sxd":              "application/vnd.sun.xml.draw",
		".std":              "application/vnd.sun.xml.draw.template",
		".sxi":              "application/vnd.sun.xml.impress",
		".sti":              "application/vnd.sun.xml.impress.template",
		".sxm":              "application/vnd.sun.xml.math",
		".sxw":              "application/vnd.sun.xml.writer",
		".sxg":              "application/vnd.sun.xml.writer.global",
		".stw":              "application/vnd.sun.xml.writer.template",
		".sus":              "application/vnd.sus-calendar",
		".susp":             "application/vnd.sus-calendar",
		".svd":              "application/vnd.svd",
		".sis":              "application/vnd.symbian.install",
		".sisx":             "application/vnd.symbian.install",
		".xsm":              "application/vnd.syncml+xml",
		".bdm":              "application/vnd.syncml.dm+wbxml",
		".xdm":              "application/vnd.syncml.dm+xml",
		".tao":              "application/vnd.tao.intent-module-archive",
		".pcap":             "application/vnd.tcpdump.pcap",
		".cap":              "application/vnd.tcpdump.pcap",
		".dmp":              "application/vnd.tcpdump.pcap",
		".tmo":              "application/vnd.tmobile-livetv",
		".tpt":              "application/vnd.trid.tpt",
		".mxs":              "application/vnd.triscape.mxs",
		".tra":              "application/vnd.trueapp",
		".ufd":              "application/vnd.ufdl",
		".ufdl":             "application/vnd.ufdl",
		".utz":              "application/vnd.uiq.theme",
		".umj":              "application/vnd.umajin",
		".unityweb":         "application/vnd.unity",
		".uoml":             "application/vnd.uoml+xml",
		".vcx":              "application/vnd.vcx",
		".vsd":              "application/vnd.visio",
		".vst":              "application/vnd.visio",
		".vss":              "application/vnd.visio",
		".vsw":              "application/vnd.visio",
		".vis":              "application/vnd.visionary",
		".vsf":              "application/vnd.vsf",
		".wbxml":            "application/vnd.wap.wbxml",
		".wmlc":             "application/vnd.wap.wmlc",
		".wmlsc":            "application/vnd.wap.wmlscriptc",
		".wtb":              "application/vnd.webturbo",
		".nbp":              "application/vnd.wolfram.player",
		".wpd":              "application/vnd.wordperfect",
		".wqd":              "application/vnd.wqd",
		".stf":              "application/vnd.wt.stf",
		".xar":              "application/vnd.xara",
		".xfdl":             "application/vnd.xfdl",
		".hvd":              "application/vnd.yamaha.hv-dic",
		".hvs":              "application/vnd.yamaha.hv-script",
		".hvp":              "application/vnd.yamaha.hv-voice",
		".osf":              "application/vnd.yamaha.openscoreformat",
		".osfpvg":           "application/vnd.yamaha.openscoreformat.osfpvg+xml",
		".saf":              "application/vnd.yamaha.smaf-audio",
		".spf":              "application/vnd.yamaha.smaf-phrase",
		".cmp":              "application/vnd.yellowriver-custom-menu",
		".zir":              "application/vnd.zul",
		".zirz":             "application/vnd.zul",
		".zaz":              "application/vnd.zzazz.deck+xml",
		".vxml":             "application/voicexml+xml",
		".wgt":              "application/widget",
		".hlp":              "application/winhlp",
		".wspolicy":         "application/wspolicy+xml",
		".7z":               "application/x-7z-compressed",
		".abw":              "application/x-abiword",
		".ace":              "application/x-ace-compressed",
		".dmg":              "application/x-apple-diskimage",
		".aab":              "application/x-authorware-bin",
		".x32":              "application/x-authorware-bin",
		".u32":              "application/x-authorware-bin",
		".vox":              "application/x-authorware-bin",
		".aam":              "application/x-authorware-map",
		".aas":              "application/x-authorware-seg",
		".torrent":          "application/x-bittorrent",
		".blb":              "application/x-blorb",
		".blorb":            "application/x-blorb",
		".bz":               "application/x-bzip",
		".bz2":              "application/x-bzip2",
		".boz":              "application/x-bzip2",
		".cbr":              "application/x-cbr",
		".cba":              "application/x-cbr",
		".cbt":              "application/x-cbr",
		".cbz":              "application/x-cbr",
		".cb7":              "application/x-cbr",
		".vcd":              "application/x-cdlink",
		".cfs":              "application/x-cfs-compressed",
		".chat":             "application/x-chat",
		".pgn":              "application/x-chess-pgn",
		".nsc":              "application/x-conference",
		".deb":              "application/x-debian-package",
		".udeb":             "application/x-debian-package",
		".dgc":              "application/x-dgc-compressed",
		".dir":              "application/x-director",
		".dcr":              "application/x-director",
		".dxr":              "application/x-director",
		".cst":              "application/x-director",
		".cct":              "application/x-director",
		".cxt":              "application/x-director",
		".w3d":              "application/x-director",
		".fgd":              "application/x-director",
		".swa":              "application/x-director",
		".wad":              "application/x-doom",
		".ncx":              "application/x-dtbncx+xml",
		".dtb":              "application/x-dtbook+xml",
		".res":              "application/x-dtbresource+xml",
		".evy":              "application/x-envoy",
		".eva":              "application/x-eva",
		".bdf":              "application/x-font-bdf",
		".gsf":              "application/x-font-ghostscript",
		".psf":              "application/x-font-linux-psf",
		".pcf":              "application/x-font-pcf",
		".snf":              "application/x-font-snf",
		".pfa":              "application/x-font-type1",
		".pfb":              "application/x-font-type1",
		".pfm":              "application/x-font-type1",
		".afm":              "application/x-font-type1",
		".arc":              "application/x-freearc",
		".spl":              "application/x-futuresplash",
		".gca":              "application/x-gca-compressed",
		".ulx":              "application/x-glulx",
		".gnumeric":         "application/x-gnumeric",
		".gramps":           "application/x-gramps-xml",
		".install":          "application/x-install-instructions",
		".iso":              "application/x-iso9660-image",
		".jnlp":             "application/x-java-jnlp-file",
		".lzh":              "application/x-lzh-compressed",
		".lha":              "application/x-lzh-compressed",
		".mie":              "application/x-mie",
		".prc":              "application/x-mobipocket-ebook",
		".mobi":             "application/x-mobipocket-ebook",
		".application":      "application/x-ms-application",
		".lnk":              "application/x-ms-shortcut",
		".wmd":              "application/x-ms-wmd",
		".wmz":              "application/x-msmetafile",
		".xbap":             "application/x-ms-xbap",
		".mdb":              "application/x-msaccess",
		".obd":              "application/x-msbinder",
		".crd":              "application/x-mscardfile",
		".clp":              "application/x-msclip",
		".com":              "application/x-msdownload",
		".msi":              "application/x-msdownload",
		".mvb":              "application/x-msmediaview",
		".m13":              "application/x-msmediaview",
		".m14":              "application/x-msmediaview",
		".wmf":              "application/x-msmetafile",
		".emf":              "application/x-msmetafile",
		".emz":              "application/x-msmetafile",
		".mny":              "application/x-msmoney",
		".pub":              "application/x-mspublisher",
		".scd":              "application/x-msschedule",
		".trm":              "application/x-msterminal",
		".wri":              "application/x-mswrite",
		".nzb":              "application/x-nzb",
		".p7b":              "application/x-pkcs7-certificates",
		".spc":              "application/x-pkcs7-certificates",
		".p7r":              "application/x-pkcs7-certreqresp",
		".rar":              "application/x-rar-compressed",
		".ris":              "application/x-research-info-systems",
		".xap":              "application/x-silverlight-app",
		".sql":              "application/x-sql",
		".sit":              "application/x-stuffit",
		".sitx":             "application/x-stuffitx",
		".srt":              "application/x-subrip",
		".t3":               "application/x-t3vm-image",
		".gam":              "application/x-tads",
		".tfm":              "application/x-tex-tfm",
		".der":              "application/x-x509-ca-cert",
		".crt":              "application/x-x509-ca-cert",
		".fig":              "application/x-xfig",
		".xlf":              "application/x-xliff+xml",
		".xpi":              "application/x-xpinstall",
		".xz":               "application/x-xz",
		".z1":               "application/x-zmachine",
		".z2":               "application/x-zmachine",
		".z3":               "application/x-zmachine",
		".z4":               "application/x-zmachine",
		".z5":               "application/x-zmachine",
		".z6":               "application/x-zmachine",
		".z7":               "application/x-zmachine",
		".z8":               "application/x-zmachine",
		".xaml":             "application/xaml+xml",
		".xdf":              "application/xcap-diff+xml",
		".xenc":             "application/xenc+xml",
		".xhtml":            "application/xhtml+xml",
		".xht":              "application/xhtml+xml",
		".dtd":              "application/xml-dtd",
		".xop":              "application/xop+xml",
		".xpl":              "application/xproc+xml",
		".xslt":             "application/xslt+xml",
		".xspf":             "application/xspf+xml",
		".mxml":             "application/xv+xml",
		".xhvml":            "application/xv+xml",
		".xvml":             "application/xv+xml",
		".xvm":              "application/xv+xml",
		".yang":             "application/yang",
		".yin":              "application/yin+xml",
		".adp":              "audio/adpcm",
		".mid":              "audio/midi",
		".midi":             "audio/midi",
		".kar":              "audio/midi",
		".rmi":              "audio/midi",
		".m4a":              "audio/mp4a-latm",
		".mp4a":             "audio/mp4",
		".m4p":              "audio/mp4a-latm",
		".mpga":             "audio/mpeg",
		".mp2a":             "audio/mpeg",
		".m2a":              "audio/mpeg",
		".m3a":              "audio/mpeg",
		".oga":              "audio/ogg",
		".ogg":              "audio/ogg",
		".spx":              "audio/ogg",
		".s3m":              "audio/s3m",
		".sil":              "audio/silk",
		".uva":              "audio/vnd.dece.audio",
		".uvva":             "audio/vnd.dece.audio",
		".eol":              "audio/vnd.digital-winds",
		".dra":              "audio/vnd.dra",
		".dts":              "audio/vnd.dts",
		".dtshd":            "audio/vnd.dts.hd",
		".lvp":              "audio/vnd.lucent.voice",
		".pya":              "audio/vnd.ms-playready.media.pya",
		".ecelp4800":        "audio/vnd.nuera.ecelp4800",
		".ecelp7470":        "audio/vnd.nuera.ecelp7470",
		".ecelp9600":        "audio/vnd.nuera.ecelp9600",
		".rip":              "audio/vnd.rip",
		".weba":             "audio/webm",
		".aac":              "audio/x-aac",
		".caf":              "audio/x-caf",
		".flac":             "audio/x-flac",
		".mka":              "audio/x-matroska",
		".wax":              "audio/x-ms-wax",
		".wma":              "audio/x-ms-wma",
		".rmp":              "audio/x-pn-realaudio-plugin",
		".xm":               "audio/xm",
		".cdx":              "chemical/x-cdx",
		".cif":              "chemical/x-cif",
		".cmdf":             "chemical/x-cmdf",
		".cml":              "chemical/x-cml",
		".csml":             "chemical/x-csml",
		".xyz":              "chemical/x-xyz",
		".ttc":              "font/collection",
		".otf":              "font/otf",
		".ttf":              "font/ttf",
		".woff":             "font/woff",
		".woff2":            "font/woff2",
		".cgm":              "image/cgm",
		".g3":               "image/g3fax",
		".jp2":              "image/jp2",
		".ktx":              "image/ktx",
		".pict":             "image/pict",
		".pic":              "image/x-pict",
		".pct":              "image/x-pict",
		".btif":             "image/prs.btif",
		".sgi":              "image/sgi",
		".svgz":             "image/svg+xml",
		".psd":              "image/vnd.adobe.photoshop",
		".uvi":              "image/vnd.dece.graphic",
		".uvvi":             "image/vnd.dece.graphic",
		".uvg":              "image/vnd.dece.graphic",
		".uvvg":             "image/vnd.dece.graphic",
		".djvu":             "image/vnd.djvu",
		".djv":              "image/vnd.djvu",
		".sub":              "text/vnd.dvb.subtitle",
		".dwg":              "image/vnd.dwg",
		".dxf":              "image/vnd.dxf",
		".fbs":              "image/vnd.fastbidsheet",
		".fpx":              "image/vnd.fpx",
		".fst":              "image/vnd.fst",
		".mmr":              "image/vnd.fujixerox.edmics-mmr",
		".rlc":              "image/vnd.fujixerox.edmics-rlc",
		".mdi":              "image/vnd.ms-modi",
		".wdp":              "image/vnd.ms-photo",
		".npx":              "image/vnd.net-fpx",
		".wbmp":             "image/vnd.wap.wbmp",
		".xif":              "image/vnd.xiff",
		".webp":             "image/webp",
		".3ds":              "image/x-3ds",
		".cmx":              "image/x-cmx",
		".fh":               "image/x-freehand",
		".fhc":              "image/x-freehand",
		".fh4":              "image/x-freehand",
		".fh5":              "image/x-freehand",
		".fh7":              "image/x-freehand",
		".pntg":             "image/x-macpaint",
		".pnt":              "image/x-macpaint",
		".mac":              "image/x-macpaint",
		".sid":              "image/x-mrsid-image",
		".pcx":              "image/x-pcx",
		".qtif":             "image/x-quicktime",
		".qti":              "image/x-quicktime",
		".tga":              "image/x-tga",
		".mime":             "message/rfc822",
		".igs":              "model/iges",
		".iges":             "model/iges",
		".msh":              "model/mesh",
		".mesh":             "model/mesh",
		".silo":             "model/mesh",
		".dae":              "model/vnd.collada+xml",
		".dwf":              "model/vnd.dwf",
		".gdl":              "model/vnd.gdl",
		".gtw":              "model/vnd.gtw",
		".mts":              "model/vnd.mts",
		".vtu":              "model/vnd.vtu",
		".wrl":              "model/vrml",
		".vrml":             "model/vrml",
		".x3db":             "model/x3d+binary",
		".x3dbz":            "model/x3d+binary",
		".x3dv":             "model/x3d+vrml",
		".x3dvz":            "model/x3d+vrml",
		".x3d":              "model/x3d+xml",
		".x3dz":             "model/x3d+xml",
		".manifest":         "text/cache-manifest",
		".appcache":         "text/cache-manifest",
		".ics":              "text/calendar",
		".ifb":              "text/calendar",
		".n3":               "text/n3",
		".text":             "text/plain",
		".conf":             "text/plain",
		".def":              "text/plain",
		".list":             "text/plain",
		".log":              "text/plain",
		".in":               "text/plain",
		".dsc":              "text/prs.lines.tag",
		".ttl":              "text/turtle",
		".uri":              "text/uri-list",
		".uris":             "text/uri-list",
		".urls":             "text/uri-list",
		".vcard":            "text/vcard",
		".curl":             "text/vnd.curl",
		".dcurl":            "text/vnd.curl.dcurl",
		".mcurl":            "text/vnd.curl.mcurl",
		".scurl":            "text/vnd.curl.scurl",
		".fly":              "text/vnd.fly",
		".flx":              "text/vnd.fmi.flexstor",
		".gv":               "text/vnd.graphviz",
		".3dml":             "text/vnd.in3d.3dml",
		".spot":             "text/vnd.in3d.spot",
		".jad":              "text/vnd.sun.j2me.app-descriptor",
		".wml":              "text/vnd.wap.wml",
		".wmls":             "text/vnd.wap.wmlscript",
		".s":                "text/x-asm",
		".asm":              "text/x-asm",
		".cc":               "text/x-c",
		".cxx":              "text/x-c",
		".cpp":              "text/x-c",
		".hh":               "text/x-c",
		".dic":              "text/x-c",
		".f":                "text/x-fortran",
		".for":              "text/x-fortran",
		".f77":              "text/x-fortran",
		".f90":              "text/x-fortran",
		".java":             "text/x-java-source",
		".nfo":              "text/x-nfo",
		".opml":             "text/x-opml",
		".p":                "text/x-pascal",
		".pas":              "text/x-pascal",
		".sfv":              "text/x-sfv",
		".uu":               "text/x-uuencode",
		".vcs":              "text/x-vcalendar",
		".3gp":              "video/3gpp",
		".3g2":              "video/3gpp2",
		".h261":             "video/h261",
		".h263":             "video/h263",
		".h264":             "video/h264",
		".jpgv":             "video/jpeg",
		".jpm":              "video/jpm",
		".jpgm":             "video/jpm",
		".mj2":              "video/mj2",
		".mjp2":             "video/mj2",
		".ts":               "video/mp2t",
		".mp4v":             "video/mp4",
		".mpg4":             "video/mp4",
		".m4v":              "video/x-m4v",
		".m2v":              "video/mpeg",
		".ogv":              "video/ogg",
		".uvh":              "video/vnd.dece.hd",
		".uvvh":             "video/vnd.dece.hd",
		".uvm":              "video/vnd.dece.mobile",
		".uvvm":             "video/vnd.dece.mobile",
		".uvp":              "video/vnd.dece.pd",
		".uvvp":             "video/vnd.dece.pd",
		".uvs":              "video/vnd.dece.sd",
		".uvvs":             "video/vnd.dece.sd",
		".uvv":              "video/vnd.dece.video",
		".uvvv":             "video/vnd.dece.video",
		".dvb":              "video/vnd.dvb.file",
		".fvt":              "video/vnd.fvt",
		".mxu":              "video/vnd.mpegurl",
		".m4u":              "video/vnd.mpegurl",
		".pyv":              "video/vnd.ms-playready.media.pyv",
		".uvu":              "video/vnd.uvvu.mp4",
		".uvvu":             "video/vnd.uvvu.mp4",
		".viv":              "video/vnd.vivo",
		".dv":               "video/x-dv",
		".dif":              "video/x-dv",
		".f4v":              "video/x-f4v",
		".fli":              "video/x-fli",
		".flv":              "video/x-flv",
		".mkv":              "video/x-matroska",
		".mk3d":             "video/x-matroska",
		".mks":              "video/x-matroska",
		".mng":              "video/x-mng",
		".asf":              "video/x-ms-asf",
		".asx":              "video/x-ms-asf",
		".vob":              "video/x-ms-vob",
		".wm":               "video/x-ms-wm",
		".wmv":              "video/x-ms-wmv",
		".wmx":              "video/x-ms-wmx",
		".wvx":              "video/x-ms-wvx",
		".smv":              "video/x-smv",
		".ice":              "x-conference/x-cooltalk",
	}
	for k, v := range commonCustomMimeType {
		err := mime.AddExtensionType(k, v)
		if err != nil {

		}
	}
}

func PathApiExclude() []string {
	return []string{
		`^.*/api/v[0-9\.]+/ping$`,
		`^.*/?/?adm/api/v[0-9\.]+/keycloak/connect$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/saml/metadata/[0-9A-z\.\_\-]+$`,
		`^.*/?/?adm/api/v[0-9\.]+/login$`,
		`^.*/?/?adm/api/v[0-9\.]+/sso/basic/login$`,
		`^.*/?/?adm/api/v[0-9\.]+/sso/login/by-action$`,
		`^.*/?/?adm/api/v[0-9\.]+/sso/provider/basic$`,
		`^.*/?/?adm/api/v[0-9\.]+/sso/saml/login$`,
		`^.*/?/?adm/api/v[0-9\.]+/logout$`,
		`^.*/?/?adm/api/v[0-9\.]+/account/forget-password$`,
		`^.*/?/?adm/api/v[0-9\.]+/account/forget-password/verify$`,
		`^.*/?/?adm/api/v[0-9\.]+/account/forget-password/change-password$`,
		`^.*/?/?adm/api/v[0-9\.]+/account/pattern/username$`,
		`^.*/?/?adm/api/v[0-9\.]+/account/verify-email/send-mail$`,
		`^.*/?/?adm/api/v[0-9\.]+/account/verify-email/check-code$`,
		`^.*/?/?adm/api/v[0-9\.]+/forget-password/check-code/verify-email$`,
		`^.*/?/?adm/api/v[0-9\.]+/forget-password/send-code/verify-email$`,
		`^.*/?/?adm/api/v[0-9\.]+/partners$`,
		`^.*/?/?adm/api/v[0-9\.]+/partners/insert-id$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/login$`,
		`^.*/?/?adm/web/api/v[0-9\.]+/account/forget-password$`,
		`^.*/?/?adm/web/api/v[0-9\.]+/account/forget-password/verify$`,
		`^.*/?/?adm/web/api/v[0-9\.]+/account/forget-password/change-password$`,
		`^.*/?/?adm/web/api/v[0-9\.]+/account/pattern/username$`,
		`^.*/?/?adm/web/api/v[0-9\.]+/account/verify-email/check-code$`,
		`^.*/?/?adm/web/api/v[0-9\.]+/account/verify-email/send-mail$`,
		`^.*/?/?adm/web/api/v[0-9\.]+/forget-password/check-code/verify-email$`,
		`^.*/?/?adm/web/api/v[0-9\.]+/forget-password/send-code/verify-email$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/account/forget-password$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/account/forget-password/verify$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/account/forget-password/change-password$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/account/pattern/username$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/forget-password/send-code/verify-email$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/forget-password/check-code/verify-email$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/account/check-email$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/account/check-email-verify$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/account/verify-email/send-mail$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/account/verify-email/check-code$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/accounts/mobio/private$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/login$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/sso/saml/login$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/logout$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/merchants/[0-9A-z\.\_\-]+/modules/details$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/sale/rm/detail$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/push-id$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/accounts/module/paging$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/biometrics/verify$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/accounts/module/paging$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/accounts/eib/level-lower$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/manage/area/by-sol$`,
		`^.*/?/?adm/api/v[0-9\.]+/license/server/info$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/saml/login$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/saml/acs/[0-9A-z\.\_\-]+$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/saml/slo/[0-9A-z\.\_\-]+$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/saml/logout$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/saml/login$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/[0-9A-z\.\_\-]+/callback$`,
		`^.*/?/?callcenter/api/v[0-9\.]+/callcenter/events$`,
		`^.*/?/?callcenter/ringtone/[0-9A-z\.\_\-]+/[0-9A-z\.\_\-]+$`,
		`^.*/?/?callcenter/partner/api/v[0-9\.]+/event-call$`,
		`^.*/?/?callcenter/partner/api/v[0-9\.]+/event-call/stringee$`,
		`^.*/?/?callcenter/partner/api/v[0-9\.]+/event-call/[0-9A-z\.\_\-]+/callback$`,
		`^.*/?/?callcenter/ringtone/$`,
		`^.*/?/?chattool/api/v[0-9\.]+/chat/[0-9A-z\.\_\-]+/config$`,
		`^.*/?/?chattool/api/v[0-9\.]+/chat/[0-9A-z\.\_\-]+/get_message$`,
		`^.*/?/?chattool/api/v[0-9\.]+/chat/[0-9A-z\.\_\-]+/message_welcome$`,
		`^.*/?/?chattool/api/v[0-9\.]+/chat/report/session/avgtime/[0-9A-z\.\_\-]+$`,
		`^.*/?/?chattool/api/v[0-9\.]+/chat/report/session/num/[0-9A-z\.\_\-]+$`,
		`^.*/?/?chattool/api/v[0-9\.]+/domain/connection$`,
		`^.*/?/?chattool/api/v[0-9\.]+/domain/image/[0-9A-z\.\_\-]+$`,
		`^.*/?/?chattool/api/v[0-9\.]+/message/upload_files$`,
		`^.*/?/?chattool/api/v[0-9\.]+/receive/message/supporter$`,
		`^.*/?/?chattool/api/v[0-9\.]+/session/[0-9A-z\.\_\-]+/actions/end$`,
		`^.*/?/?chattool/api/v[0-9\.]+/session/[0-9A-z\.\_\-]+/actions/rating$`,
		`^.*/?/?chattool/api/v[0-9\.]+/visitor/save$`,
		`^.*/?/?chattool/api/v[0-9\.]+/visitor/update/last_read$`,
		`^.*/?/?chattool/static/uploads/.*$`,
		`^.*/?/?emk/images/[0-9A-z\.\_\-]+$`,
		`^.*/?/?mailclient/webhook/nepo.gif$`,
		`^.*/?/?product/static/[0-9A-z\.\_\-]+$`,
		`^.*/?/?profiling/internal/v[0-9\.]+/web_hook/open_single_email$`,
		`^.*/?/?profiling/internal/v[0-9\.]+/web_hook/single_email$`,
		`^.*/?/?profiling/v[0-9\.]+/companies$`,
		// `^.*/?/?profiling/v[0-9\.]+/profile/find_by_satellite$`,
		`^.*/?/?profiling/v[0-9\.]+/download/excel/folders/[0-9A-z\.\_\-]+/files/[0-9A-z\.\_\-]+$`,
		`^.*/?/?profiling/v[0-9\.]+/download/folders/[0-9A-z\.\_\-\/]+/files/[0-9A-z\.\_\-\/]+$`,
		`^.*/?/?profiling/v[0-9\.]+/download/folders/exports/files/[0-9A-z\.\_\-]+$`,
		//`^.*/?/?profiling/v[0-9\.]+/export/excel$`,
		//`^.*/?/?profiling/v[0-9\.]+/export/users$`,
		`^.*/?/?profiling/v[0-9\.]+/frequently_demands$`,
		`^.*/?/?profiling/v[0-9\.]+/hobby$`,
		`^.*/?/?profiling/v[0-9\.]+/jobs$`,
		`^.*/?/?profiling/v[0-9\.]+/marital_status$`,
		`^.*/?/?profiling/v[0-9\.]+/merchants/[0-9A-z\.\_\-]+/users/actions/verify$`,
		`^.*/?/?profiling/v[0-9\.]+/nations$`,
		`^.*/?/?profiling/v[0-9\.]+/operations$`,
		`^.*/?/?profiling/v[0-9\.]+/provinces$`,
		`^.*/?/?profiling/v[0-9\.]+/provinces/[0-9A-z\.\_\-]+/districts$`,
		`^.*/?/?profiling/v[0-9\.]+/provinces/[0-9A-z\.\_\-]+/districts/[0-9A-z\.\_\-]+/wards$`,
		`^.*/?/?profiling/v[0-9\.]+/questions$`,
		`^.*/?/?profiling/v[0-9\.]+/users/actions/verify$`,
		`^.*/?/?rapporteur/static_file/[0-9A-z\.\_\-\/]+$`,
		`^.*/?/?sale/mobile/api/v[0-9\.]+/merchant_config_third_party$`,
		`^.*/?/?social/api/v[0-9\.]+/app$`,
		`^.*/?/?social/api/v[0-9\.]+/merchant/[0-9A-z\.\_\-]+/remove-data-assign-by-merchant$`,
		`^.*/?/?social/api/v[0-9\.]+/merchant/get-check-hashtag-post-status$`,
		`^.*/?/?social/api/v[0-9\.]+/merchant/get-general-template-reply-status$`,
		`^.*/?/?social/api/v[0-9\.]+/merchant/update-check-hashtag-post-status$`,
		`^.*/?/?social/api/v[0-9\.]+/merchant/update-general-template-reply-status$`,
		`^.*/?/?social/api/v[0-9\.]+/teams/list-team-chattool$`,
		`^.*/?/?social/report/api/v[0-9\.]+/dashboard/report/exports/[0-9A-z\.\_\-\/]+$`,
		`^.*/?/?social/static/user/[0-9A-z\.\_\-]+$`,
		`^.*/?/?social/static/message/zalo/[0-9A-z\.\_\-]+$`,
		`^.*/?/?social/static/message/instagram/[0-9A-z\.\_\-]+$`,
		`^.*/?/?social/static/message/[0-9A-z\.\_\-]+$`,
		`^.*/?/?social/static/template-reply/[0-9A-z\.\_\-]+$`,
		`^.*/?/?social/static/default/default_avatar+$`,
		`^.*/?/?social/static/page/[0-9A-z\.\_\-]+$`,
		`^.*/?/?statistics-code/profile/vouchers/number/[0-9A-z\.\_\-]+$`,
		`^.*/?/?survey/api/v[0-9\.]+/surveys/[0-9A-z\.\_\-]+/actions/submit_answer$`,
		`^.*/?/?survey/api/v[0-9\.]+/surveys/[0-9A-z\.\_\-]+/limit$`,
		`^.*/?/?survey/api/v[0-9\.]+/surveys/actions/view$`,
		`^.*/?/?voucher/api/v[0-9\.]+/statistics-code/profile/vouchers/number/[0-9A-z\.\_\-]+$`,
		`^.*/?/?voucher/api/v[0-9\.]+/statistics-code/vouchers/export/excel$`,
		`^.*/?/?voucher/static/[0-9A-z\.\_\-\/]+$`,
		`^.*/?/?voucher/static/export_excel/[0-9A-z\.\_\-]+$`,
		`^.*/?/?voucher/static/statistics-code/vouchers/export/excel$`,
		`^.*/?/?voucher/api/v[0-9\.]+/vouchers/by_link/get/info$`,
		`^.*/?/?loyalty/static/images/[0-9A-z\.\_\-]+$`,
		`^.*/?/?nm/mobile/api/v[0-9\.]+/sessions/notifies/MOBIO_APP_NOTIFY/objects/[0-9A-z._-]+`,
		`^.*/?/?nm/mobile/api/v[0-9\.]+/sessions/notifies/[0-9A-z._-]+/objects/[0-9A-z._-]+`,
		`^.*/?/?nm/mobile/api/v[0-9\.]+/sessions/notifies/[0-9A-z._-]+/read`,
		`^.*/?/?nm/mobile/api/v[0-9\.]+/sessions/notifies/MOBIO_APP_NOTIFY/objects/[0-9A-z._-]+/read`,
		`^.*/?/?nm/mobile/api/v[0-9\.]+/sessions/notifies/MOBIO_APP_NOTIFY/objects/[0-9A-z._-]+/total`,
		`^.*/?/?nm/mobile/api/v[0-9\.]+/sessions/notifies/reads`,
		`^.*/?/?voucher/api/v[0-9\.]+/qrcode/gen/[A-z0-9\_\.]+$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/login$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/sso/saml/login$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/logout$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/push-id/register$`,
		`^.*/?/?loyalty/api/v[0-9\.]+/banner$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/consent/profile/get-by-phone$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/evident-consent-by-log-id$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/avatars/get-by-log-id$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/identification-card/decryption$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/face/check$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/sms/request/confirm-consent$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/sms/receiver$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/information/config/third-party/api$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/los/merchant/config/fields$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/los/actions/filter$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/los/status$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/los/detail-by-code$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/los/status/actions/upsert$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/actions/get-history$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/actions/get-code$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/actions/get-code$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/products$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/report-status$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/actions/search-new$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/actions/upsert/save-view$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/actions/filters/save-view$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/detail-by-sheet-number/[0-9A-z\.\_\-]+$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/actions/get-file-report/[0-9A-z\.\_\-]+$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/receiver/search-results$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/quick-sales/premium-accounts/lock-unlock$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/quick-sales/check/cif$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/quick-sales/premium-account-types$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/quick-sales/premium-number-types-fee$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/quick-sales/premium-number-types$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/quick-sales/premium-accounts/actions/filter$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/quick-sales/premium-account-lengths$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/citizen-ids/read-only-information$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/citizen-ids/validate$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/face-recognition/verify$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/quick-sales/accounts/actions/filter-by-type$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/quick-sales/actions/retry$`,
		`^.*/?/?mobilebackend/api/v[0-9\.]+/exchange-rate/list$`,
		`^.*/?/?mobilebackend/mockup-data/api/v[0-9\.]+/quick-sales/view-workflow-tree$`,
		`^.*/?/?mobilebackend/mockup-data/api/v[0-9\.]+/customer-form$`,
		`^.*/?/?mobilebackend/mockup-data/api/v[0-9\.]+/delete-customer$`,
		`^.*/?/?mobilebackend/mockup-data/api/v[0-9\.]+/customers$`,
		`^.*/?/?mobilebackend/mockup-data/api/v[0-9\.]+/log-detail$`,
		`^.*/?/?market-place/api/v[0-9\.]+/google/authorized-callback$`,
		`^.*/?/?wfb/mobile/api/v[0-9\.]+/forms/config/external$`,
		`^.*/?/?wfb/mobile/api/v[0-9\.]+/forms/submission/external$`,
		`^.*/?/?communicationhub/external/api/v[0-9\.]+/message/push/app$`,
		`^.*/?/?wfb/mock-data/api/v[0-9\.]+/.*$`,
	}
}

func PathApiRedirect() []string {
	return []string{
		`^.*/?/?market-place/api/v[0-9\.]+/google/authorized-callback$`,
	}
}

func PathApiGuestAcceptedApis() []string {
	return []string{
		`^/?/?adm/api/v[0-9\.]+/account/config/notify$`,
		`^/?/?payment/external/api/v[0-9\.]+/ping$`,
		`^/?/?adm/api/v[0-9\.]+/account/module/check$`,
		`^/?/?social/api/v[0-9\.]+/teams/list-team-chattool$`,
		`^/?/?chattool/api/v[0-9\.]+/chat/[0-9A-z._-]+/config$`,
		`^/?/?chattool/api/v[0-9\.]+/chat/[0-9A-z._-]+/message_welcome$`,
		`^/?/?chattool/api/v[0-9\.]+/message/upload_files$`,
		`^/?/?chattool/api/v[0-9\.]+/visitor/save$`,
		`^/?/?chattool/api/v[0-9\.]+/chat/[0-9A-z._-]+/get_message$`,
		`^/?/?survey/api/v[0-9\.]+/web-build$`,
		`^/?/?survey/api/v[0-9\.]+/surveys/[0-9A-z._-]+/limit$`,
		`^/?/?survey/api/v[0-9\.]+/surveys/[0-9A-z._-]+/actions/submit_answer$`,
		`^/?/?market-place/external/api/v[0-9\.]+/bulk-data$`,
		`^/?/?/onpage-journey/sdk/api/v[0-9\.]+/.*`,
		`^/?/?/sale/external/api/v[0-9\.]+/integrate-los/deal/list$`,
		`^/?/?/sale/external/api/v[0-9\.]+/integrate-los/deal/update$`,
		`^/?/?/sale/external/api/v[0-9\.]+/integrate-los/deal/bulk/update$`,
		`^/?/?/mobilebackend/api/insert-result$`,
		`^/?/?/communicationhub/external/api/v[0-9\.]+/.*$`,
		`^/?/?/wfb/external/api/v[0-9\.]+/form/status/by-type$`,
		`^/?/?/wfb/external/api/v[0-9\.]+/master-data$`,
		`^/?/?/wfb/external/api/v[0-9\.]+/master-data/excel$`,
		`^/?/?/adm/partner/api/v[0-9\.]+/login$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/find_by_satellite$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/find_by_multiple_satellite$`,
	}
}

func ConfigApiGuestAcceptedApi() map[string]ConfigActionPathGuestApi {
	return map[string]ConfigActionPathGuestApi{
		`^/?/?adm/api/v[0-9\.]+/account/config/notify$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?adm/api/v[0-9\.]+/account/module/check$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?social/api/v[0-9\.]+/teams/list-team-chattool$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?chattool/api/v[0-9\.]+/chat/[0-9A-z._-]+/config$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?chattool/api/v[0-9\.]+/chat/[0-9A-z._-]+/message_welcome$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?chattool/api/v[0-9\.]+/message/upload_files$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?chattool/api/v[0-9\.]+/visitor/save$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?chattool/api/v[0-9\.]+/chat/[0-9A-z._-]+/get_message$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?survey/api/v[0-9\.]+/web-build$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?survey/api/v[0-9\.]+/surveys/[0-9A-z._-]+/limit$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?survey/api/v[0-9\.]+/surveys/[0-9A-z._-]+/actions/submit_answer$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/onpage-journey/sdk/api/v[0-9\.]+/.*`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/folders$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/folders/actions/view-tree$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/folders/[0-9A-z._-]+$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/folders/[0-9A-z._-]+/top$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/folders/[0-9A-z._-]+/breadcrumb$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/knowledge-bases$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/knowledge-bases/actions/config$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/articles$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/articles/[0-9A-z._-]+/[0-9A-z._-]+$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/tags$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/tags/actions/get-by-ids$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/articles/actions/search$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/knowledge-base/external/api/v[0-9\.]+/articles/actions/total/search$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/voucher/api/v[0-9\.]+/qrcode/gen/[0-9A-z._-]+$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/market-place/api/v[0-9\.]+/check/anonymous$`: initConfigActionPathGuestApi(
			true, true, false, true, false,
		),
		`^/?/?/market-place/api/v[0-9\.]+/webpush/tracking-code/[0-9A-z._-]+/config$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/market-place/external/api/v[0-9\.]+/bulk-data$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/profiling/external/v[0-9\.]+/device/init$`: initConfigActionPathGuestApi(
			true, true, false, false, true,
		),
		`^/?/?/profiling/external/v[0-9\.]+/device/gen_token$`: initConfigActionPathGuestApi(
			true, true, true, false, false,
		),
		`^/?/?/profiling/external/v[0-9\.]+/device/sync$`: initConfigActionPathGuestApi(
			true, true, true, true, false,
		),
		`^/?/?/profiling/external/v[0-9\.]+/device/collect$`: initConfigActionPathGuestApi(
			true, true, true, true, false,
		),
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/reports/history/exports$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/reports/history/total$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^.*/?/?mobilebackend/api/v[0-9\.]+/cic/reports/history/list$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/market-place/api/v[0-9\.]+/dataflow/connectors/live-debug$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/market-place/api/v[0-9\.]+/dataflow/connectors/live-debug/[0-9A-z._-]+$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/mobilebackend/external/api/v[0-9\.]+/quick-sales/landing-page/otp/verify$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/mobilebackend/external/api/v[0-9\.]+/quick-sales/landing-page/otp$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/mobilebackend/external/api/v[0-9\.]+/quick-sales/landing-page/submit$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/wfb/external/api/v[0-9\.]+/forms/confirm-page/cancel$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/wfb/external/api/v[0-9\.]+/forms/confirm-page/otp/verify$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/wfb/external/api/v[0-9\.]+/forms/confirm-page/otp$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/wfb/external/api/v[0-9\.]+/forms/submission/external$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
		`^/?/?/wfb/external/api/v[0-9\.]+/forms/forms/config/external$`: initConfigActionPathGuestApi(
			false, false, false, false, false,
		),
	}

}

func ExcludeCheckFileTypeApis() []string {
	return []string{
		`^/?/?landingpage/api/v[0-9\.]+/pages$`,
	}
}

func PathApiPublicPuAcceptedApis() []string {
	return []string{
		`^/?/?adm/api/v[0-9\.]+/account/config/notify$`,
		`^/?/?adm/api/v[0-9\.]+/account/module/check$`,
		`^/?/?adm/api/v[0-9\.]+/accounts$`,
		`^/?/?adm/api/v[0-9\.]+/accounts/list$`,
		`^/?/?adm/api/v[0-9\.]+/email_service/list_email$`,
		`^/?/?adm/api/v[0-9\.]+/keycloak/connect$`,
		`^/?/?adm/api/v[0-9\.]+/login$`,
		`^/?/?adm/api/v[0-9\.]+/logout$`,
		`^/?/?adm/api/v[0-9\.]+/merchants/[0-9A-z._-]+/related$`,
		`^/?/?adm/api/v[0-9\.]+/merchants/account-id$`,
		`^/?/?adm/api/v[0-9\.]+/mobile/merchants/accounts$`,
		`^/?/?adm/api/v[0-9\.]+/profile-groups$`,
		`^/?/?adm/api/v[0-9\.]+/profile-groups/for-account$`,
		`^/?/?adm/api/v[0-9\.]+/push-id$`,
		`^/?/?adm/api/v[0-9\.]+/sale/rm/detail$`,
		`^/?/?adm/api/v[0-9\.]+/team/account/permission$`,
		`^/?/?adm/api/v[0-9\.]+/team/accounts$`,
		`^/?/?adm/api/v[0-9\.]+/teams/find$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/login$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/biometrics/verify$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/sso/saml/login$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/logout$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/merchants/[0-9A-z\.\_\-]+/modules/details$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/sale/rm/detail$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/team/account/permission$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/push-id$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/email_service/list_email/used$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/google/connect$`,
		`^.*/?/?adm/mobile/api/v[0-9\.]+/outlook/connect$`,
		`^.*/?/?adm/api/v[0-9\.]+/license/server/info$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/saml/login$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/saml/acs/[0-9A-z\.\_\-]+$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/saml/slo/[0-9A-z\.\_\-]+$`,
		`^.*/?/?adm/partner/api/v[0-9\.]+/sso/saml/logout$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/filter/role-module$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/list$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/account/setting/notify$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/login$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/logout$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/manager/scope/list$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/merchants/[0-9A-z\._\-]+/all-accounts$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/merchants/account-id$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/merchants/[0-9A-z\._\-]+/modules/details$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/merchants/sub-brands/get_all$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/profile-groups/list$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/sale/rm/detail$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/push-id$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/push-id/register$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/team/accounts$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/team/accounts/detail$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/team/find/account$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/team/leader/by-account$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/teams/find$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/team/history-accounts$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/role-name$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/role-name-create$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/has-team$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/teams/account/permission$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/teams/leader/by-account$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/mobile/merchants/accounts$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/manager/scope/detail$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/account/detail-extra$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/profile-groups/list$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/field-extra$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/account/detail-extra$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/biometrics/detail$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/biometrics/register$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/eib/lv5-by-group-position$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/eib/lv6-by-group-position$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/eib/count-position$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/eib/lv56-by-group-position$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/eib/lv6-by-lv5$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/account/be-managed$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/accounts/role-lower$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/manage/info-extra$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/manage/sol/by-area$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/manage/area/by-sol$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/manage/group-position/by-level$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/team/pagination$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/team/by-ids$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/module-teams$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/merchants/config/filter-module$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/merchants/config/dynamic-filter-module$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/teams/accounts/team-module$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/account/config/mobile$`,
		`^/?/?adm/mobile/api/v[0-9\.]+/teams/by-account$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/address/list_address_type$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/[0-9A-z._-]+$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/[0-9A-z\._\-]+$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/list$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/upload_file$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/field/list$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/[0-9A-z\._\-]+/media_for_mobile$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/[0-9A-z\._\-]+/media$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/[0-9A-z\._\-]+/media/[0-9A-z\._\-]+$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/[0-9A-z\._\-]+/other_module$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/[0-9A-z\._\-]+/product_holding/detail$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/[0-9A-z\._\-]+/product_holding/list$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/list_by_id$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/list_by_id_in_company$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/list_for_assign$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/const/profile-job-title-group$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/const/profile-type-relationship$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/list_by_condition$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/check-and-create$`,
		`^/?/?company/mobile/api/v[0-9\.]+/merchant/company/list_for_assign$`,
		`^/?/?location/api/v[0-9\.]+/address/addresses$`,
		`^/?/?mailclient/api/v[0-9\.]+/send_mail/work_mail$`,
		`^/?/?mailclient/api/v[0-9\.]+/template-email/account/select$`,
		`^/?/?mailclient/api/v[0-9\.]+/signature$`,
		`^/?/?mailclient/api/v[0-9\.]+/template-email/upsert$`,
		`^/?/?mailclient/api/v[0-9\.]+/email/schedule/list$`,
		`^/?/?mailclient/api/v[0-9\.]+/email/schedule/cancel$`,
		`^/?/?nm/api/v[0-9\.]+/sessions/notifies/MOBIO_APP_NOTIFY/objects/[0-9A-z._-]+$`,
		`^/?/?nm/api/v[0-9\.]+/sessions/notifies/MOBIO_APP_NOTIFY/objects/[0-9A-z._-]+/read$`,
		`^/?/?nm/api/v[0-9\.]+/sessions/notifies/MOBIO_APP_NOTIFY/objects/[0-9A-z._-]+/total$`,
		`^/?/?nm/api/v[0-9\.]+/sessions/notifies/reads$`,
		`^/?/?note/mobile/api/v[0-9\.]+/count$`,
		`^/?/?note/mobile/api/v[0-9\.]+/files$`,
		`^/?/?note/mobile/api/v[0-9\.]+/note$`,
		`^/?/?note/mobile/api/v[0-9\.]+/note/[0-9A-z\._\-]+$`,
		`^/?/?note/mobile/api/v[0-9\.]+/note/comment$`,
		`^/?/?note/mobile/api/v[0-9\.]+/note/comment$`,
		`^/?/?note/mobile/api/v[0-9\.]+/note/comment/[0-9A-z\._\-]+$`,
		`^/?/?note/mobile/api/v[0-9\.]+/note/comment/action/filter$`,
		`^/?/?note/mobile/api/v[0-9\.]+/note/comment/files$`,
		`^/?/?note/mobile/api/v[0-9\.]+/notes$`,
		`^/?/?note/mobile/api/v[0-9\.]+/notes/action/filter$`,
		`^/?/?note/mobile/api/v[0-9\.]+/notes/type-result$`,
		`^/?/?note/mobile/api/v[0-9\.]+/notes?/[0-9A-z._-]+$`,
		`^/?/?note/mobile/api/v[0-9\.]+/notes/validate/data-add`,
		`^/?/?note/mobile/api/v[0-9\.]+/notes/type`,
		`^/?/?product/api/v[0-9\.]+/categories$`,
		`^/?/?product/api/v[0-9\.]+/categories/actions$`,
		`^/?/?product/api/v[0-9\.]+/categories/tree$`,
		`^/?/?product/api/v[0-9\.]+/products$`,
		`^/?/?product/api/v[0-9\.]+/products/[0-9A-z._-]+$`,
		`^/?/?product/api/v[0-9\.]+/products/[0-9A-z._-]+/media$`,
		`^/?/?product/api/v[0-9\.]+/products/action/categories$`,
		`^/?/?product/api/v[0-9\.]+/products/actions/detail$`,
		`^/?/?product/api/v[0-9\.]+/products/actions/list_product_by_ids$`,
		`^/?/?product/api/v[0-9\.]+/products/demo/detail$`,
		`^/?/?product/api/v[0-9\.]+/products/demo/evaluate$`,
		`^/?/?product/api/v[0-9\.]+/products/demo/khdn$`,
		`^/?/?product/api/v[0-9\.]+/products/demo/variables$`,
		`^/?/?product/api/v[0-9\.]+/products/list/demo$`,
		`^/?/?product/api/v[0-9\.]+/products/name$`,
		`^/?/?product/mobile/api/v[0-9\.]+/products/actions/get-by-categories$`,
		`^/?/?product/mobile/api/v[0-9\.]+/categories/actions/get-child-cate-by-parent-category$`,
		`^/?/?product/mobile/api/v[0-9\.]+/categories/tree$`,
		`^/?/?product/mobile/api/v[0-9\.]+/categories/actions/get-info$`,
		`^/?/?product/mobile/api/v[0-9\.]+/products/actions/details/by-ids$`,
		`^/?/?product/mobile/api/v[0-9\.]+/products$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/customer-group$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product-line$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product-line$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product-line/[0-9A-z\._\-]+/list-product-type$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product-line/actions/filter$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product-type$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product-type/action/get-by-ids$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product/action/get-by-ids$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product/actions/list-by-line-type$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/action/filter/product-line?$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product-type/action/get-list-root$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product/actions/list-by-line-type$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/merchant/field/display/action/filter$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product-line/action/filter$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/product/actions/get-list-directly-attached-product-line$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/action/file-attachment$`,
		`^/?/?product/mobile/bank/api/v[0-9\.]+/action/file-attachment/[0-9A-z\._\-]+$`,
		`^/?/?profiling/external/v[0-9\.]+/df/profile$`,
		`^/?/?profiling/external/v[0-9\.]+/merchants/[0-9A-z\._\-]+/user/profile-id$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/search_by_identify$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/search_by_phone$`,
		`^/?/?profiling/external/v[0-9\.]+/profile_owner/actions/list$`,
		`^/?/?profiling/external/v[0-9\.]+/type_customer/actions/list$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/detail_product_holding$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/list/product_holding$`,
		`^/?/?profiling/v[0-9\.]+/merchant/address/list_address_type$`,
		`^/?/?profiling/v[0-9\.]+/merchant/field/list$`,
		`^/?/?profiling/v[0-9\.]+/merchants/[0-9A-z._-]+/customers/actions/list$`,
		`^/?/?profiling/v[0-9\.]+/profile/search_by_profile_ids$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/search_by_name$`,
		`^/?/?profiling/external/v[0-9\.]+/merchant/field/list$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/search_by_profile_ids?$`,
		`^/?/?profiling/external/v[0-9\.]+/merchant/address/list_address_type$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/search_by_profile_ids/limit_field$`,
		`^/?/?profiling/external/v[0-9\.]+/merchants/[0-9A-z\._\-]+/customers/[0-9A-z\._\-]+/info$`,
		`^/?/?profiling/external/v[0-9\.]+/merchants/[0-9A-z\._\-]+/cdp_view_profile/[0-9A-z\._\-]+/info$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/search_by_cnpi$`,
		`^/?/?profiling/external/v[0-9\.]+/merchants/[0-9A-z\._\-]+/customers/actions/list_assign$`,
		`^/?/?profiling/external/v[0-9\.]+/df/profile$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/search_by_phone$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/search_by_name$`,
		`^/?/?profiling/external/v[0-9\.]+/merchant/profile/[0-9A-z\._\-]+/media$`,
		`^/?/?profiling/external/v[0-9\.]+/merchant/profile/[0-9A-z\._\-]+/media/[0-9A-z\._\-]+$`,
		`^/?/?profiling/external/v[0-9\.]+/merchant/profile/[0-9A-z\._\-]+/modify_media/[0-9A-z\._\-]+$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/consent_nfc/[0-9A-z\._\-]+$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/consent/[0-9A-z\._\-]+$`,
		`^/?/?profiling/external/v[0-9\.]+/unification/search`,
		`^/?/?profiling/v[0-9\.]+/profile/search_by_name$`,
		`^/?/?profiling/v[0-9\.]+/merchant/profile/[0-9A-z\._\-]+/media$`,
		`^/?/?profiling/external/v[0-9\.]+/df/profile/[0-9A-z\._\-]+$`,
		`^/?/?profiling/v[0-9\.]+/profile/detail_product_holding$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/detail_product_holding$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/find_by_satellite$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/find_by_conditions$`,
		`^/?/?profiling/external/v[0-9\.]+/profile/check_by_conditions$`,
		`^/?/?profiling/external/v[0-9\.]+/df/check_n_create$`,
		`^/?/?profiling/external/v[0-9\.]+/merchant/company/list_for_assign$`,
		`^/?/?profiling/external/v[0-9\.]+/customers/actions/upsert$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/auto/team-account$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/campaigns$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/campaigns/assignee$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/configs/mapping/field-profile$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/assignees$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z._-]+/action/filter/activity$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z._-]+/change/steps$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z._-]+/detail$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z._-]+/media$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z._-]+/sale_steps$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z._-]+/update/business_customers$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z._-]+/update/customers$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z._-]+/update/personal_customers$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/action/filter/activity$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/actions/event$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/change/steps$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/detail$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/media$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/update$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/add$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/add/business_customers$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/add/personal_customers$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/assignment$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/filter$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/forward$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/actions/list/get/merchant_id$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/actions/un_new$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/assignment-status$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/filter/save-view$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/list_by_ids$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/kpi/list$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/merchant/field/list$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/merchant_config_third_party$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/other/search-profile-by-ids$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/report/campaign$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/report/states$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/sale_processes$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/save-view-config$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/save-view-config/groups$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/save-view-configs$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/save-view-config/order$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/state-code/unique-name$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/state-code/unique-name/report$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/states/filter$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/states/filter-save-view$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/v2.1/accounts$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/dashboard/performance-process-deal$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/dashboard/funnel$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/dashboard/pipeline$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/dashboard/deals$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/dashboard/export/deal-funnel$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/dashboard/export/deal-pipeline$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/dashboard/export/performance-process-deal$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deal/statistic/filter$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/media/count$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/rule-deal-change-state/[0-9A-z\._\-]+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/sale-process/configs-product-tree$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/actions/list/get/company_ids$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/filter/total-wait-by-sale-process$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/merchant/field/display$`,
		`^/?/?sale/rm/detail$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/call-reports/[0-9A-z\._\-]+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/call-reports$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/call-report/process$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/call-report/fields/filter$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/filter/list$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/change-state$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/dashboard/deal/product-line$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/actions/list/get/profile_ids$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/rule-deal-change-state/detail-by-process-ids$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/media/[0-9A-z\._\-]+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/save-view-configs+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/sale_processes+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deal/statistic/filter+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/assignment+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/change-state+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/delete+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/action/forward+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/merchant/field/list+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/action/filter/activity+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/detail+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/sale_processes/[0-9A-z\._\-]+/states+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/media+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/actions/change_state+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deal/source+$`,
		`^/?/?sale/mobile/api/v[0-9\.]+/deals/[0-9A-z\._\-]+/actions/change_state+$`,
		`^/?/?tag/api/v[0-9\.]+/tags$`,
		`^/?/?tag/api/v[0-9\.]+/tags/work_assign/list$`,
		`^/?/?tag/api/v[0-9\.]+/tags/work_assign/ids$`,
		`^/?/?tag/api/v[0-9\.]+/tags/work_assign$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/[0-9A-z\._\-]+$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/comment$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/comment/[0-9A-z\._\-]+$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/comment/action/filter$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/comment/files$`,
		`^/?/?task/mobile/api/v[0-9\.]+/files$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/[0-9A-z\._\-]+$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/action/read-from-task/[0-9A-z\._\-]+$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/action/read-from-other/[0-9A-z\._\-]+$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/count_by_status$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/done$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/unchecked_done$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/action/filter$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/action/list-from-task/filter$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions/list-from-task/filter$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/count_by_assign_status$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/need-handle-today$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/action/search-by-name$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/count/filter$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/change-status-process$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/count-from-task-by-status$`,
		`^/?/?task/mobile/api/v[0-9\.]+/merchant/field/list$`,
		`^/?/?task/mobile/api/v[0-9\.]+/merchant/field/list-by-task-type$`,
		`^/?/?task/mobile/api/v[0-9\.]+/save-views$`,
		`^/?/?task/mobile/api/v[0-9\.]+/save-views/groups$`,
		`^/?/?task/mobile/api/v[0-9\.]+/save-views/order$`,
		`^/?/?task/mobile/api/v[0-9\.]+/save-views/display$`,
		`^/?/?task/mobile/api/v[0-9\.]+/save-views/tasks/count$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/v2/actions/list-from-other$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/list-status-process$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions/list-status-process$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/[0-9A-z\._\-]+/remove-child$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/action/quickview$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/action/get-list-relate$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/action/total-list-relate$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/validate/data-add$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/actions/[0-9A-z\._\-]+$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions/delete$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/action/delete$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions/find-by-ids$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/actions/read-from-task/[0-9A-z\._\-]+$`,
		`^/?/?task/mobile/api/v[0-9\.]+/task/actions/read-from-other/[0-9A-z\._\-]+$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions/un-new$`,
		`^/?/?task/mobile/api/v[0-9\.]+/staff/menu/config$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/sync-comment-count$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions/quickview$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions/get-list-relate$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions/total-list-relate$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/get-data-field-task-by-ids$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions/update-multi$`,
		`^/?/?task/mobile/api/v[0-9\.]+/tasks/actions/assign-multi-task$`,
		`^/?/?task/mobile/api/v[0-9\.]+/merchant/task-type$`,
		`^/?/?task/mobile/api/v[0-9\.]+/merchant/task-type/[0-9A-z\._\-]+$`,
		`^/?/?task/mobile/api/v[0-9\.]+/merchant/list-task-type$`,
		`^/?/?task/mobile/api/v[0-9\.]+/merchant/task-type/change-status$`,
		`^/?/?task/mobile/api/v[0-9\.]+/merchant/task-type/detail-by-code$`,
		`^/?/?task/mobile/api/v[0-9\.]+/merchant/task-types/by-codes$`,
		`^/?/?ticket/mobile/api/v[0-9\.]+/tickets/actions/filter$`,
		`^/?/?ticket/mobile/api/v[0-9\.]+/ticket/actions/get-data-by-ids$`,
		`^/?/?ticket/mobile/api/v[0-9\.]+/merchant/field/list$`,
		`^/?/?ticket/mobile/api/v[0-9\.]+/tickets/actions/get-data-by-ids$`,
		`^/?/?ticket/api/v[0-9\.]+/ticket/actions/get-data-by-ids$`,
		`^/?/?ticket/api/v[0-9\.]+/tickets/actions/filter$`,
		`^/?/?nm/mobile/api/v[0-9\.]+/sessions/notifies/[0-9A-z\._\-]+/objects/[0-9A-z\._\-]+$`,
		`^/?/?nm/mobile/api/v[0-9\.]+/sessions/notifies/[0-9A-z\._\-]+/objects/[0-9A-z\._\-]+$/read`,
		`^/?/?nm/mobile/api/v[0-9\.]+/sessions/notifies/[0-9A-z\._\-]+/objects/[0-9A-z\._\-]+$/total`,
		`^/?/?nm/mobile/api/v[0-9\.]+/sessions/notifies/reads`,
		`^/?/?nm/api/v[0-9\.]+/sessions/notifies/[0-9A-z._-]+/objects/[0-9A-z._-]+$`,
		`^/?/?nm/api/v[0-9\.]+/sessions/notifies/[0-9A-z._-]+/read$`,
		`^/?/?loyalty/api/v[0-9\.]+/stores$`,
		`^/?/?loyalty/mobile/api/v[0-9\.]+/transactions/staff$`,
		`^/?/?loyalty/mobile/api/v[0-9\.]+/news$`,
		`^/?/?loyalty/mobile/api/v[0-9\.]+/news/[0-9A-z._-]+$`,
		`^/?/?/transaction/external/api/v[0-9\.]+/transactions/staff$`,
		`^/?/?/transaction/external/api/v[0-9\.]+/add/list$`,
		`^/?/?/location/mobile/api/v[0-9\.]+/address/addresses$`,
		`^/?/?/company/mobile/api/v[0-9\.]+/merchant/company/[0-9A-z._-]+/product_holding/detail$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/performance-score/overview$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/gap-targets$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/rank-point-performance$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/rank-completion-rate$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/result-complete-detail$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/estimate-price$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/expected-salary$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/targets/config-display$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/completion-rate/staff-title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/synthetic/group-manager$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/target-performance/detail-staff$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/target-performance/year$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/target-performance/department$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/point-used-salary$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/completion-rate/business-unit$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/rank-completion-rate/business-department$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/target-performance/rbo/department$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/complete-target/staff/department$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/average-revenue/department$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/target-performance/rm$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/target-performance/rbo$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/last-report-date$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level3/average-productivity/target$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level3/completion-rate/staff-title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level3/completion-rate$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level3/rank/completion-rate$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level2/top5/rm$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level2/top5/department/target$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level2/completion-rate$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level2/completion-rate/staff-title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level2/completion-rate/rm/business-unit$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level2/completion-rate/rm/target$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level2/average-productivity/target$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level1/completion-rate/staff-title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level1/average-productivity/target$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level1/completion-rate/rm/business-unit$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level1/top5/rm$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level1/top5/department/target$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level1/completion-rate/rm/target$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level1/completion-rate$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/completion-rate/segment$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/target/list$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/config/filter$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/completion-rate/segment$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/completion-rate/business-unit/title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/rank-completion-rate/business-department/title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/average-revenue/department/title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/complete-target/staff/department/title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level3/average-productivity/target/title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level2/average-productivity/target/title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level1/average-productivity/target/title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/completion-rate/segment/title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level2/top5/department/target/title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/level1/top5/department/target/title$`,
		`^/?/?/kpi-management/mobile/api/v[0-9\.]+/dashboard/target-performance/target/department/title$`,
		`^/?/?/voucher/mobile/api/v[0-9\.]+/code-used-history/profile$`,
		`^/?/?/voucher/mobile/api/v[0-9\.]+/voucher/profile$`,
		`^/?/?/voucher/mobile/api/v[0-9\.]+/code-used-history/profile$`,
		`^/?/?/voucher/mobile/api/v[0-9\.]+/code/profile/voucher_id$`,
		`^/?/?/voucher/mobile/api/v[0-9\.]+/voucher/profile/detail$`,
		`^/?/?/voucher/mobile/api/v[0-9\.]+/categories$`,
		`^/?/?/callcenter/api/v[0-9\.]+/app/call/list_call$`,
		`^/?/?/comment/mobile/api/v[0-9\.]+/comments$`,
		`^/?/?/comment/mobile/api/v[0-9\.]+/comments/[0-9A-z._-]+$`,
		`^/?/?/comment/mobile/api/v[0-9\.]+/comments/action/filter+$`,
		`^/?/?/comment/mobile/api/v[0-9\.]+/comments/action/count+$`,
		`^/?/?/comment/mobile/api/v[0-9\.]+/comments/action/reaction+$`,
		`^/?/?/comment/mobile/api/v[0-9\.]+/comments/action/get-list-comment-from-other-module+$`,
		`^/?/?/comment/mobile/api/v[0-9\.]+/file-attachment+$`,
		`^/?/?/comment/mobile/api/v[0-9\.]+/file-attachment/action/upload-multi+$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/[0-9A-z\._\-]+$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/used$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/submission$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/field/call-api$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/submission/records$`,
		`^/?/?/knowledge-base/external/mobile/api/v[0-9\.]+/knowledge-bases$`,
		`^/?/?/knowledge-base/external/mobile/api/v[0-9\.]+/tags$`,
		`^/?/?/knowledge-base/external/mobile/api/v[0-9\.]+/tags/actions/get-by-ids$`,
		`^/?/?/knowledge-base/external/mobile/api/v[0-9\.]+/articles/actions/search$`,
		`^/?/?/knowledge-base/external/mobile/api/v[0-9\.]+/articles/actions/total/search$`,
		`^/?/?/wfb/mobile/partner/api/v[0-9\.]+/forms/call-api$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/link-preview/reload$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/link-preview$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/submission-info$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/submission/permissions-add$`,
		`^/?/?/wfb/mobile/get-data/api/v[0-9\.]+/form/savings-account$`,
		`^/?/?/wfb/mobile/get-data/api/v[0-9\.]+/form/identify-doc$`,
		`^/?/?/wfb/mobile/get-data/api/v[0-9\.]+/forms/constant-data$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/submission/permissions-add$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/ecm/download$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/upload-attachments$`,
		`^/?/?/wfb/mobile/get-data/api/v[0-9\.]+/forms/call-api$`,
		`^/?/?/wfb/mobile/api/v[0-9\.]+/forms/[0-9A-z\._\-]+/config-api/[0-9A-z\._\-]+$`,
		`^/?/?/wfb/mobile/get-data/api/v[0-9\.]+/forms/check-cif$`,
		`^/?/?/wfb/mobile/get-data/api/v[0-9\.]+/forms/debit-account$`,
		`^/?/?mobilebackend/api/v[0-9\.]+/cic/actions/get-history$`,
		`^/?/?mobilebackend/api/v[0-9\.]+/cic/actions/get-code$`,
		`^/?/?mobilebackend/api/v[0-9\.]+/cic/products$`,
		`^/?/?mobilebackend/api/v[0-9\.]+/cic/report-status$`,
		`^/?/?mobilebackend/api/v[0-9\.]+/cic/actions/search-new$`,
		`^/?/?mobilebackend/api/v[0-9\.]+/cic/actions/upsert/save-view$`,
		`^/?/?mobilebackend/api/v[0-9\.]+/cic/actions/filters/save-view$`,
		`^/?/?mobilebackend/api/v[0-9\.]+/cic/detail-by-sheet-number/[0-9A-z\.\_\-]+$`,
		`^/?/?mobilebackend/api/v[0-9\.]+/cic/actions/get-file-report/[0-9A-z\.\_\-]+$`,
		`^/?/?mobilebackend/api/v[0-9\.]+/cic/receiver/search-results$`,
	}
}

func PathApiBlackListByMerchantCode(merchantCode string, method string) []string {
	// Khởi tạo cấu hình blacklist
	configPath := map[string]map[string][]string{
		"HDB": {
			"POST": {
				`^.*/sale/api/v[0-9\.]+/call-reports$`,
				`^.*/sale/api/v[0-9\.]+/call-report/process$`,
				`^.*/sale/mobile/api/v[0-9\.]+/call-reports$`,
				`^.*/sale/mobile/api/v[0-9\.]+/call-report/process$`,
			},
			"PUT": {
				`^.*/sale/api/v[0-9\.]+/call-reports/[0-9A-z._-]+$`,
				`^.*/sale/mobile/api/v[0-9\.]+/call-reports/[0-9A-z._-]+$`,
			},
		},
	}

	if methodsMap, exists := configPath[merchantCode]; exists {
		if patterns, methodExists := methodsMap[method]; methodExists {
			return patterns
		}
	}

	return []string{}
}
