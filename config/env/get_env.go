package env

import (
	"github.com/caarlos0/env"
)

type RedisConf struct {
	RedisUri      string `env:"REDIS_URI"`
	AdminRedisUri string `env:"ADMIN_REDIS_URI"`
	AdminRedisType string `env:"ADMIN_REDIS_TYPE"`
	RedisBaseUri string `env:"REDIS_BASE_URI"`
	RedisBaseType string `env:"REDIS_BASE_TYPE"`
	RedisPort     int    `env:"REDIS_PORT" envDefault:"3000"`
	RedisKeyCache string `env:"PREFIX_KEY" envDefault:"apim"`
	TimeSecondCachePermission int `env:"TIME_SECOND_CACHE_PERMISSION" envDefault:"86400"`
}

func ConfigEnvRedis() RedisConf {
	cfg := RedisConf{}
	err := env.Parse(&cfg)
	if err != nil {
		return RedisConf{}
	}
	return cfg
}

type ApplicationConfig struct {
	VmType     string `env:"VM_TYPE" envDefault:"MOBIO"`
	AdminHost  string `env:"ADMIN_HOST"`
	PathFolder string `env:"HOME_PATH"`
	PowerPem   string `env:"POWER_PEM" envDefault:""`
	SendPem   bool `env:"SEND_PEM" envDefault:"false"`
	YEK_REWOP string `env:"YEK_REWOP"`
}

func GetApplicationConfig() ApplicationConfig {
	cfg := ApplicationConfig{}
	err := env.Parse(&cfg)
	if err != nil {
		return ApplicationConfig{}
	}
	return cfg
}
