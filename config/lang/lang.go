package lang

import (
	"api-m/common"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

type StructureMessage struct {
	Message string
	Code    int
}

func buildJsonPathByLang(lang string) string {
	FolderConfigLang := common.SetConfigFolder().FolderConfigLanguage
	FileFolderConfigLange := filepath.Join(FolderConfigLang, fmt.Sprintf("%v.json", strings.TrimSpace(lang)))
	return FileFolderConfigLange
}

func readFileJsonToStructMessage(lang string) map[string]StructureMessage {
	pathFile := buildJsonPathByLang(lang)
	jsonFile, err := os.Open(pathFile)
	if err != nil {
		fmt.Println(err)
	}
	defer jsonFile.Close()

	byteValue, _ := ioutil.ReadAll(jsonFile)

	var results map[string]StructureMessage

	json.Unmarshal(byteValue, &results)
	return results
}

func GetMessageByKey(key string, req *http.Request) StructureMessage {
	lang := req.URL.Query().Get("lang")
	if lang == "" {
		lang = common.DefaultLang
	}
	MessageConfig := readFileJsonToStructMessage(lang)
	for k, v := range MessageConfig {
		if k == key {
			return v
		}
	}
	return StructureMessage{}

}
