{"bad_request": {"message": "Bad request.", "code": 400}, "unauthentice": {"message": "JWT is invalid or is expired. Please login again.", "code": 401}, "not_allowed": {"message": "you can't execute, please login before execution this api.", "code": 405}, "not_found": {"message": "Item is not found.", "code": 404}, "internal_server_error": {"message": "There is an internal server error.", "code": 500}, "validate_error": {"message": "Validation failed.", "code": 412}, "pem_error": {"message": "Validation pem failed.", "code": 412}, "unauthorization": {"message": "You have not permission to using this function!", "code": 403}, "service_not_available": {"message": "Service not available"}, "limit_per_page": {"message": "The number of records is too large", "code": 413}}